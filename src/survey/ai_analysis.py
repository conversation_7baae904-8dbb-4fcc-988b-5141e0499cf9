import openai
import json
from typing import Dict, List, Any, Optional
from django.conf import settings
from django.utils import timezone
from .models import SurveySession, SurveyResponse, Survey
from logger.utils import log_ai_analysis, log_error_with_context


class SurveyAIAnalyzer:
    """AI-powered analysis of survey responses using OpenAI."""
    
    def __init__(self):
        # Initialize OpenAI client
        if hasattr(settings, 'OPENAI_API_KEY'):
            openai.api_key = settings.OPENAI_API_KEY
        else:
            # Fallback to environment variable
            import os
            openai.api_key = os.getenv('OPENAI_API_KEY')
    
    def analyze_session(self, session: SurveySession) -> Dict[str, Any]:
        """Analyze a completed survey session and generate insights."""
        if not session.is_complete:
            raise ValueError("Cannot analyze incomplete survey session")
        
        try:
            # Prepare analysis data
            analysis_data = self._prepare_analysis_data(session)
            
            # Generate AI insights
            insights = self._generate_insights(session.survey, analysis_data)
            
            # Calculate performance metrics
            performance_metrics = self._calculate_performance_metrics(session, analysis_data)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(session.survey, analysis_data, performance_metrics)
            
            # Compile final analysis
            final_analysis = {
                'analysis_timestamp': timezone.now().isoformat(),
                'overall_score': session.score_percentage,
                'performance_level': self._determine_performance_level(session),
                'insights': insights,
                'performance_metrics': performance_metrics,
                'recommendations': recommendations,
                'strengths': self._identify_strengths(analysis_data),
                'areas_for_improvement': self._identify_improvement_areas(analysis_data),
                'detailed_breakdown': self._create_detailed_breakdown(session, analysis_data)
            }
            
            # Save analysis to session
            session.ai_analysis_results = final_analysis
            session.ai_analysis_completed = True
            session.save()
            
            # Log the analysis
            log_ai_analysis(
                'survey_analysis',
                {'session_id': str(session.id), 'survey_type': session.survey.survey_type},
                final_analysis,
                session.user
            )
            
            return final_analysis
            
        except Exception as e:
            log_error_with_context(e, 'survey_ai_analysis', session.user, {
                'session_id': str(session.id),
                'survey_id': session.survey.id
            })
            raise
    
    def _prepare_analysis_data(self, session: SurveySession) -> Dict[str, Any]:
        """Prepare survey response data for AI analysis."""
        responses = session.responses.all().select_related('question', 'choice_value')
        
        analysis_data = {
            'survey_type': session.survey.survey_type,
            'survey_title': session.survey.title,
            'total_questions': len(session.question_order),
            'completion_time': str(session.time_spent) if session.time_spent else None,
            'responses': []
        }
        
        for response in responses:
            question = response.question
            response_data = {
                'question_id': question.id,
                'question_text': question.question_text,
                'question_type': question.question_type,
                'weight': question.weight,
                'response_value': response.get_display_value(),
                'numeric_score': response.calculated_score,
                'max_possible_score': response.get_max_score()
            }
            
            # Add context based on question type
            if question.question_type in ['LIKERT_5', 'LIKERT_7']:
                response_data['scale_interpretation'] = self._interpret_likert_score(
                    response.numeric_value, question.question_type
                )
            elif question.question_type == 'RATING_10':
                response_data['rating_interpretation'] = self._interpret_rating_score(response.numeric_value)
            
            analysis_data['responses'].append(response_data)
        
        return analysis_data
    
    def _generate_insights(self, survey: Survey, analysis_data: Dict[str, Any]) -> List[str]:
        """Generate AI-powered insights using OpenAI."""
        try:
            # Prepare prompt based on survey type
            prompt = self._create_analysis_prompt(survey, analysis_data)
            
            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self._get_system_prompt(survey.survey_type)},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            
            insights_text = response.choices[0].message.content
            
            # Parse insights into list
            insights = [insight.strip() for insight in insights_text.split('\n') if insight.strip()]
            
            return insights[:10]  # Limit to top 10 insights
            
        except Exception as e:
            log_error_with_context(e, 'openai_insights_generation', None, {
                'survey_id': survey.id,
                'error_type': 'ai_insights'
            })
            return ["AI analysis temporarily unavailable. Please check back later."]
    
    def _create_analysis_prompt(self, survey: Survey, analysis_data: Dict[str, Any]) -> str:
        """Create a detailed prompt for AI analysis."""
        prompt = f"""
        Analyze the following {survey.get_survey_type_display()} survey responses:
        
        Survey: {survey.title}
        Total Questions: {analysis_data['total_questions']}
        Completion Time: {analysis_data.get('completion_time', 'Not recorded')}
        
        Response Details:
        """
        
        for response in analysis_data['responses']:
            prompt += f"""
        Q: {response['question_text']}
        Type: {response['question_type']}
        Response: {response['response_value']}
        Score: {response['numeric_score']}/{response['max_possible_score']}
        """
        
        prompt += f"""
        
        Please provide:
        1. Key insights about communication performance
        2. Specific strengths demonstrated
        3. Areas needing improvement
        4. Actionable recommendations
        5. Overall assessment summary
        
        Focus on {survey.get_survey_type_display().lower()} communication skills.
        """
        
        return prompt
    
    def _get_system_prompt(self, survey_type: str) -> str:
        """Get system prompt based on survey type."""
        base_prompt = "You are an expert communication skills assessor and coach."
        
        if survey_type == 'FACE_TO_FACE':
            return f"{base_prompt} Analyze face-to-face communication skills including verbal clarity, non-verbal communication, eye contact, engagement, and overall presence."
        elif survey_type == 'PHONE':
            return f"{base_prompt} Analyze phone communication skills including vocal clarity, phone etiquette, audio quality, and effectiveness without visual cues."
        elif survey_type == 'ZOOM':
            return f"{base_prompt} Analyze video conferencing communication skills including technical setup, virtual presence, platform proficiency, and digital engagement."
        else:
            return f"{base_prompt} Analyze general communication and survey responses to provide meaningful insights."
    
    def _calculate_performance_metrics(self, session: SurveySession, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate detailed performance metrics."""
        responses = analysis_data['responses']
        
        # Category-based scoring
        category_scores = {}
        for response in responses:
            question_type = response['question_type']
            if question_type not in category_scores:
                category_scores[question_type] = {'total': 0, 'max': 0, 'count': 0}
            
            category_scores[question_type]['total'] += response['numeric_score'] or 0
            category_scores[question_type]['max'] += response['max_possible_score']
            category_scores[question_type]['count'] += 1
        
        # Calculate percentages
        category_percentages = {}
        for category, scores in category_scores.items():
            if scores['max'] > 0:
                category_percentages[category] = (scores['total'] / scores['max']) * 100
            else:
                category_percentages[category] = 0
        
        return {
            'overall_percentage': session.score_percentage,
            'category_breakdown': category_percentages,
            'total_responses': len(responses),
            'completion_rate': 100.0,  # Since session is complete
            'average_response_quality': self._calculate_response_quality(responses)
        }
    
    def _generate_recommendations(self, survey: Survey, analysis_data: Dict[str, Any], metrics: Dict[str, Any]) -> List[str]:
        """Generate specific recommendations based on performance."""
        recommendations = []
        
        # Performance-based recommendations
        overall_score = metrics['overall_percentage']
        
        if overall_score >= 80:
            recommendations.append("Excellent performance! Continue to maintain these high standards.")
        elif overall_score >= 60:
            recommendations.append("Good performance with room for targeted improvements.")
        else:
            recommendations.append("Focus on fundamental skill development for significant improvement.")
        
        # Category-specific recommendations
        category_breakdown = metrics['category_breakdown']
        
        for category, percentage in category_breakdown.items():
            if percentage < 60:
                if category in ['LIKERT_5', 'LIKERT_7']:
                    recommendations.append(f"Focus on improving areas measured by agreement scales - consider additional training.")
                elif category == 'RATING_10':
                    recommendations.append(f"Work on skills measured by rating scales - practice and feedback needed.")
                elif category == 'YES_NO':
                    recommendations.append(f"Review fundamental practices - some basic requirements not being met.")
        
        # Survey type-specific recommendations
        if survey.survey_type == 'FACE_TO_FACE':
            recommendations.extend([
                "Practice maintaining appropriate eye contact during conversations",
                "Work on synchronizing verbal and non-verbal communication",
                "Consider recording practice sessions to review body language"
            ])
        elif survey.survey_type == 'PHONE':
            recommendations.extend([
                "Focus on vocal clarity and speaking pace for phone communication",
                "Practice phone etiquette and professional greetings",
                "Ensure good audio setup and quiet environment"
            ])
        elif survey.survey_type == 'ZOOM':
            recommendations.extend([
                "Optimize camera positioning and lighting setup",
                "Practice virtual engagement techniques",
                "Learn to effectively use video conferencing features"
            ])
        
        return recommendations[:8]  # Limit to 8 recommendations
    
    def _determine_performance_level(self, session: SurveySession) -> str:
        """Determine performance level based on score."""
        score = session.score_percentage
        
        if score >= 90:
            return "Exceptional"
        elif score >= 80:
            return "Excellent"
        elif score >= 70:
            return "Good"
        elif score >= 60:
            return "Satisfactory"
        elif score >= 50:
            return "Needs Improvement"
        else:
            return "Requires Significant Development"
    
    def _identify_strengths(self, analysis_data: Dict[str, Any]) -> List[str]:
        """Identify key strengths from responses."""
        strengths = []
        responses = analysis_data['responses']
        
        # Find high-scoring responses
        high_scores = [r for r in responses if r['numeric_score'] and r['max_possible_score'] and 
                      (r['numeric_score'] / r['max_possible_score']) >= 0.8]
        
        for response in high_scores[:5]:  # Top 5 strengths
            strengths.append(f"Strong performance in: {response['question_text'][:60]}...")
        
        return strengths
    
    def _identify_improvement_areas(self, analysis_data: Dict[str, Any]) -> List[str]:
        """Identify areas needing improvement."""
        improvement_areas = []
        responses = analysis_data['responses']
        
        # Find low-scoring responses
        low_scores = [r for r in responses if r['numeric_score'] and r['max_possible_score'] and 
                     (r['numeric_score'] / r['max_possible_score']) <= 0.6]
        
        for response in low_scores[:5]:  # Top 5 improvement areas
            improvement_areas.append(f"Needs attention: {response['question_text'][:60]}...")
        
        return improvement_areas
    
    def _create_detailed_breakdown(self, session: SurveySession, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create detailed breakdown of performance."""
        return {
            'total_score': session.total_score,
            'max_possible_score': session.max_possible_score,
            'score_percentage': session.score_percentage,
            'question_count': len(analysis_data['responses']),
            'completion_time': str(session.time_spent) if session.time_spent else None,
            'score_breakdown': session.score_breakdown
        }
    
    def _interpret_likert_score(self, score: float, question_type: str) -> str:
        """Interpret Likert scale scores."""
        if question_type == 'LIKERT_5':
            if score >= 4:
                return "Positive agreement"
            elif score >= 3:
                return "Neutral"
            else:
                return "Negative agreement"
        elif question_type == 'LIKERT_7':
            if score >= 5:
                return "Positive agreement"
            elif score >= 3:
                return "Neutral"
            else:
                return "Negative agreement"
        return "Unknown"
    
    def _interpret_rating_score(self, score: float) -> str:
        """Interpret rating scale scores."""
        if score >= 8:
            return "Excellent"
        elif score >= 6:
            return "Good"
        elif score >= 4:
            return "Average"
        else:
            return "Below average"
    
    def _calculate_response_quality(self, responses: List[Dict[str, Any]]) -> float:
        """Calculate overall response quality score."""
        if not responses:
            return 0.0
        
        total_quality = 0
        for response in responses:
            if response['numeric_score'] and response['max_possible_score']:
                quality = (response['numeric_score'] / response['max_possible_score']) * 100
                total_quality += quality
        
        return total_quality / len(responses)
