from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from survey.models import Survey, SurveyQuestion, SurveyQuestionChoice, SurveyCategory
from logger.utils import survey_logger


class Command(BaseCommand):
    help = 'Create correct benchmark surveys based on actual documents with 4-point scale'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=str,
            help='Username to assign as survey creator (defaults to first superuser)',
        )
        parser.add_argument(
            '--replace',
            action='store_true',
            help='Replace existing surveys',
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating correct benchmark survey templates...')

        # Get or create user
        if options['user']:
            try:
                user = User.objects.get(username=options['user'])
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User "{options["user"]}" not found')
                )
                return
        else:
            user = User.objects.filter(is_superuser=True).first()
            if not user:
                self.stdout.write(
                    self.style.ERROR('No superuser found. Please create one first.')
                )
                return

        # Get or create benchmark category
        category, created = SurveyCategory.objects.get_or_create(
            name='ESSSuper Communication Benchmarks',
            defaults={
                'description': 'Self-assessment tools for communication skills evaluation',
                'created_by': user,
                'slug': 'esssuper-communication-benchmarks'
            }
        )

        if options['replace']:
            # Delete existing surveys
            Survey.objects.filter(category=category).delete()
            self.stdout.write('Deleted existing benchmark surveys')

        # Create correct benchmark surveys
        self.create_face_to_face_benchmark(user, category)
        self.create_phone_benchmark(user, category)
        self.create_zoom_benchmark(user, category)

        self.stdout.write(
            self.style.SUCCESS('Successfully created correct benchmark surveys!')
        )

    def create_face_to_face_benchmark(self, user, category):
        """Create Face-to-Face benchmark with correct structure."""
        survey, created = Survey.objects.get_or_create(
            title='ESSSuper Self-Assessment Face to Face',
            created_by=user,
            defaults={
                'description': '''Self-assessment tool to help staff accurately evaluate their communication skills and identify growth opportunities in face-to-face interactions.

Duration: 15-20 minutes
Timing: Initial assessment 1-week post-workshop, then monthly for first 3 months, then quarterly.

Scoring: 1-4 scale where 1=Developing, 2=Progressing, 3=Proficient, 4=Advanced
Total possible score: 40 points (10 questions × 4 points each)''',
                'survey_type': 'FACE_TO_FACE',
                'category': category,
                'enable_scoring': True,
                'enable_ai_analysis': True,
                'is_published': True,
                'scoring_rubric': {
                    'tier_1': {'min': 10, 'max': 19, 'description': 'Tier 1 - Foundation Builder'},
                    'tier_2': {'min': 20, 'max': 29, 'description': 'Tier 2 - Developing Practitioner'},
                    'tier_3': {'min': 30, 'max': 35, 'description': 'Tier 3 - Proficient Communicator'},
                    'tier_4': {'min': 36, 'max': 40, 'description': 'Tier 4 - Expert Practitioner'}
                }
            }
        )

        if not created:
            self.stdout.write(f'Face-to-Face survey already exists: {survey.title}')
            return

        # Section A: Professional Presence & Environment Management (3 questions)
        questions_data = [
            {
                'text': 'Physical Positioning & Space - Rate your ability to create a welcoming, professional physical environment',
                'section': 'A. Professional Presence & Environment Management',
                'reflection_prompts': [
                    'How you typically arrange seating for member meetings',
                    'What do you do when a member seems uncomfortable with the physical space?',
                    'How do you maintain appropriate professional distance while showing warmth?'
                ],
                'evidence_examples': [
                    'I consistently position myself at an angle rather than directly across from members',
                    'I offer water/tissues proactively and have them easily accessible',
                    'I adjust lighting or seating when I notice member discomfort'
                ]
            },
            {
                'text': 'Body Language & Non-Verbal Communication - Rate your awareness and control of your own body language during member interactions',
                'section': 'A. Professional Presence & Environment Management',
                'reflection_prompts': [
                    'How do you know when your body language is communicating empathy vs. defensiveness?',
                    'Describe a time when you consciously adjusted your posture during a difficult conversation',
                    'What physical habits do you have that might distract from member communication?'
                ],
                'evidence_examples': [
                    'I maintain open posture even when discussing difficult topics',
                    'I notice and adjust when I cross my arms or lean back',
                    'I use forward lean and nodding to show active engagement'
                ]
            },
            {
                'text': 'Managing Visual Distress - Rate your ability to respond appropriately when members show visible emotional distress',
                'section': 'A. Professional Presence & Environment Management',
                'reflection_prompts': [
                    'How do you handle your own emotional response when a member becomes tearful?',
                    'What do you do when a member\'s body language suggests they\'re overwhelmed?',
                    'How do you maintain professional boundaries while showing genuine care?'
                ],
                'evidence_examples': [
                    'I remain calm and grounded when members show strong emotions',
                    'I offer appropriate comfort (tissues, brief pause) without overstepping',
                    'I can continue the conversation professionally while acknowledging distress'
                ]
            }
        ]

        # Add LEAPS Framework questions (Section B - 5 questions)
        leaps_questions = [
            {
                'text': 'Listen - Visual Active Listening - Rate your ability to demonstrate attentive listening through body language and visual cues',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do members typically respond to your listening style?',
                    'What visual cues tell you that you\'re truly hearing what a member is saying?',
                    'When do you find it hardest to maintain active listening posture?'
                ],
                'evidence_examples': [
                    'I maintain appropriate eye contact without staring',
                    'I use subtle nodding and facial expressions to encourage sharing',
                    'I avoid looking at papers/screens when members are speaking emotionally'
                ]
            },
            {
                'text': 'Empathise - Visual Empathy Demonstration - Rate your ability to show genuine understanding through facial expressions and presence',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do you show empathy without appearing overly emotional yourself?',
                    'Describe how your facial expressions change when discussing difficult topics',
                    'What helps you maintain genuine empathy during repetitive or frustrating conversations?'
                ],
                'evidence_examples': [
                    'My facial expressions naturally reflect appropriate concern',
                    'I can show understanding without taking on the member\'s emotional state',
                    'Members often comment that they feel heard and understood'
                ]
            },
            {
                'text': 'Ask - Sensitive Questioning Techniques - Rate your ability to ask clarifying questions while maintaining member dignity',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do you approach asking personal questions about finances or health?',
                    'What techniques do you use to make invasive questions feel less intrusive?',
                    'How do you handle when members become defensive about your questions?'
                ],
                'evidence_examples': [
                    'I preface sensitive questions with empathetic context',
                    'I explain why information is needed before asking for it',
                    'I give members time to process questions before expecting answers'
                ]
            },
            {
                'text': 'Paraphrase - Confirming Understanding Visually - Rate your ability to reflect back what you\'ve heard while reading member responses',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do you know when your paraphrasing has accurately captured the member\'s concern?',
                    'What visual cues from members tell you they feel understood vs. misunderstood?',
                    'How do you handle when your paraphrase doesn\'t match their intended meaning?'
                ],
                'evidence_examples': [
                    'I watch for nods, relief, or continued tension when I paraphrase',
                    'I invite correction if I haven\'t captured their meaning accurately',
                    'I use their emotional language, not just the factual content'
                ]
            },
            {
                'text': 'Summarise - Clear Resolution Planning - Rate your ability to create clear, collaborative next steps while maintaining visual engagement',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do you ensure members understand and agree with next steps?',
                    'What do you do when a member\'s body language suggests confusion about the plan?',
                    'How do you balance thoroughness with not overwhelming members?'
                ],
                'evidence_examples': [
                    'I use written notes or diagrams to reinforce verbal summaries',
                    'I check for understanding by watching facial expressions, not just asking',
                    'I provide take-away materials that reinforce our conversation'
                ]
            }
        ]

        # Add Resilience & Self-Care questions (Section C - 2 questions)
        resilience_questions = [
            {
                'text': 'Managing Your Own Emotional Response - Rate your ability to maintain professional composure while processing member distress',
                'section': 'C. Resilience & Self-Care in Face-to-Face Settings',
                'reflection_prompts': [
                    'What physical sensations tell you when you\'re becoming emotionally affected?',
                    'How do you reset between difficult face-to-face appointments?',
                    'What strategies help you maintain appropriate emotional boundaries?'
                ],
                'evidence_examples': [
                    'I can feel my own stress rising and take subtle steps to manage it',
                    'I use breathing techniques that don\'t distract from the member',
                    'I debrief with colleagues when needed without breaking confidentiality'
                ]
            },
            {
                'text': 'Post-Interaction Recovery - Rate your effectiveness at processing and recovering from challenging face-to-face interactions',
                'section': 'C. Resilience & Self-Care in Face-to-Face Settings',
                'reflection_prompts': [
                    'What do you do immediately after a particularly difficult member meeting?',
                    'How do you prevent one difficult interaction from affecting the next?',
                    'What signs tell you when you need additional support after member meetings?'
                ],
                'evidence_examples': [
                    'I have a consistent routine for transitioning between member appointments',
                    'I know when to seek supervisor support vs. handling independently',
                    'I can identify my stress triggers and have specific coping strategies'
                ]
            }
        ]

        # Combine all questions and create them
        all_questions = questions_data + leaps_questions + resilience_questions
        self._create_questions_with_scale(survey, all_questions)
        self.stdout.write(f'Created Face-to-Face survey with {len(all_questions)} questions')

    def create_phone_benchmark(self, user, category):
        """Create Phone benchmark with correct structure."""
        survey, created = Survey.objects.get_or_create(
            title='ESSSuper Self-Assessment Phone',
            created_by=user,
            defaults={
                'description': '''Self-assessment tool to help staff accurately evaluate their communication skills and identify growth opportunities in phone interactions.

Duration: 15-20 minutes
Timing: Initial assessment 1-week post-workshop, then monthly for first 3 months, then quarterly.

Scoring: 1-4 scale where 1=Developing, 2=Progressing, 3=Proficient, 4=Advanced
Total possible score: 40 points (10 questions × 4 points each)''',
                'survey_type': 'PHONE',
                'category': category,
                'enable_scoring': True,
                'enable_ai_analysis': True,
                'is_published': True,
                'scoring_rubric': {
                    'tier_1': {'min': 10, 'max': 19, 'description': 'Tier 1 - Foundation Builder'},
                    'tier_2': {'min': 20, 'max': 29, 'description': 'Tier 2 - Developing Practitioner'},
                    'tier_3': {'min': 30, 'max': 35, 'description': 'Tier 3 - Proficient Communicator'},
                    'tier_4': {'min': 36, 'max': 40, 'description': 'Tier 4 - Expert Practitioner'}
                }
            }
        )

        if not created:
            self.stdout.write(f'Phone survey already exists: {survey.title}')
            return

        # Section A: Audio Quality & Professional Phone Presence (3 questions)
        questions_data = [
            {
                'text': 'Audio Environment & Technical Setup - Rate your ability to create optimal audio conditions for member calls',
                'section': 'A. Audio Quality & Professional Phone Presence',
                'reflection_prompts': [
                    'How do you ensure consistent audio quality for member calls?',
                    'What do you do when background noise or technical issues arise during emotional conversations?',
                    'How do you test and maintain your phone setup for professional interactions?'
                ],
                'evidence_examples': [
                    'I consistently use a quiet, controlled environment for member calls',
                    'I have backup audio solutions when primary systems fail',
                    'I test audio quality before important calls and adjust as needed'
                ]
            },
            {
                'text': 'Vocal Tone & Professional Delivery - Rate your ability to convey empathy and professionalism through voice alone',
                'section': 'A. Audio Quality & Professional Phone Presence',
                'reflection_prompts': [
                    'How do you adjust your vocal tone to match member emotional states?',
                    'What vocal techniques do you use to show empathy without visual cues?',
                    'How do you maintain professional warmth when you can\'t see member reactions?'
                ],
                'evidence_examples': [
                    'I modulate my voice to convey appropriate empathy and concern',
                    'I use vocal pacing and pauses effectively to allow member processing',
                    'Members often comment on feeling heard and supported through phone interactions'
                ]
            },
            {
                'text': 'Managing Audio-Only Emotional Distress - Rate your ability to respond to member distress when you can only hear, not see',
                'section': 'A. Audio Quality & Professional Phone Presence',
                'reflection_prompts': [
                    'How do you recognize emotional distress through voice alone?',
                    'What do you do when you hear a member becoming upset but can\'t see their body language?',
                    'How do you provide comfort and support through voice when members are distressed?'
                ],
                'evidence_examples': [
                    'I can identify emotional changes through vocal cues and breathing patterns',
                    'I offer appropriate verbal comfort and check-ins when I hear distress',
                    'I remain calm and grounding when members express strong emotions over the phone'
                ]
            }
        ]

        # Add LEAPS Framework questions (Section B - 5 questions)
        leaps_questions = [
            {
                'text': 'Listen - Audio-Focused Active Listening - Rate your ability to demonstrate attentive listening through vocal responses and silence',
                'section': 'B. LEAPS Framework Application (Phone Context)',
                'reflection_prompts': [
                    'How do you show active listening when members can\'t see your body language?',
                    'What vocal cues do you use to encourage member sharing over the phone?',
                    'How do you handle silence and pauses during phone conversations?'
                ],
                'evidence_examples': [
                    'I use verbal acknowledgments like "mm-hmm" and "I understand" more frequently',
                    'I ask clarifying questions to show I\'m following their story',
                    'I\'m comfortable with silence and don\'t rush to fill pauses'
                ]
            },
            {
                'text': 'Empathise - Vocal Empathy Expression - Rate your ability to convey genuine understanding through voice tone and words',
                'section': 'B. LEAPS Framework Application (Phone Context)',
                'reflection_prompts': [
                    'How do you ensure your empathy comes through clearly in your voice?',
                    'What verbal techniques do you use to show you understand member emotions?',
                    'How do you handle the challenge of conveying care without visual connection?'
                ],
                'evidence_examples': [
                    'I use empathetic language that reflects back member emotions',
                    'My voice naturally conveys warmth and concern when members share difficulties',
                    'I verbalize understanding more explicitly than I would in person'
                ]
            },
            {
                'text': 'Ask - Sensitive Phone Questioning - Rate your ability to ask personal questions compassionately over the phone',
                'section': 'B. LEAPS Framework Application (Phone Context)',
                'reflection_prompts': [
                    'How do you make invasive questions feel less intrusive over the phone?',
                    'What do you do when you sense member discomfort but can\'t see their reactions?',
                    'How do you pace sensitive questions when you only have audio cues?'
                ],
                'evidence_examples': [
                    'I provide more context and explanation before asking sensitive questions',
                    'I check for understanding and comfort more frequently during phone calls',
                    'I give members extra time to process and respond to difficult questions'
                ]
            },
            {
                'text': 'Paraphrase - Audio Confirmation of Understanding - Rate your ability to reflect understanding through verbal paraphrasing',
                'section': 'B. LEAPS Framework Application (Phone Context)',
                'reflection_prompts': [
                    'How do you know when your paraphrasing has accurately captured member concerns over the phone?',
                    'What vocal cues from members tell you they feel understood vs. misunderstood?',
                    'How do you handle misunderstandings that arise from audio-only communication?'
                ],
                'evidence_examples': [
                    'I paraphrase more frequently to ensure understanding without visual confirmation',
                    'I listen for vocal agreement, hesitation, or correction when I summarize',
                    'I explicitly ask for confirmation when I sense any uncertainty'
                ]
            },
            {
                'text': 'Summarise - Clear Phone-Based Action Planning - Rate your ability to create clear next steps through phone conversation',
                'section': 'B. LEAPS Framework Application (Phone Context)',
                'reflection_prompts': [
                    'How do you ensure members understand action plans discussed over the phone?',
                    'What techniques do you use to make phone-based planning collaborative?',
                    'How do you follow up to confirm understanding after phone conversations?'
                ],
                'evidence_examples': [
                    'I summarize action steps clearly and ask members to repeat back key points',
                    'I send written follow-up emails after phone calls to confirm agreements',
                    'I schedule follow-up calls to check progress on phone-based plans'
                ]
            }
        ]

        # Add Resilience & Self-Care questions (Section C - 2 questions)
        resilience_questions = [
            {
                'text': 'Managing Phone-Based Emotional Impact - Rate your ability to process member distress when you can only hear their pain',
                'section': 'C. Phone-Specific Resilience & Self-Care',
                'reflection_prompts': [
                    'How do you handle the intensity of hearing member distress without visual context?',
                    'What strategies help you maintain emotional boundaries during difficult phone calls?',
                    'How do you reset between challenging phone conversations?'
                ],
                'evidence_examples': [
                    'I can maintain professional composure while processing member emotions over the phone',
                    'I use breathing and grounding techniques that don\'t interfere with the call',
                    'I debrief appropriately after difficult phone interactions'
                ]
            },
            {
                'text': 'Vocal and Emotional Stamina - Rate your ability to maintain vocal and emotional energy across multiple phone interactions',
                'section': 'C. Phone-Specific Resilience & Self-Care',
                'reflection_prompts': [
                    'How do you maintain vocal quality and energy throughout the day?',
                    'What do you do to recover between emotionally challenging phone calls?',
                    'How do you prevent phone-based emotional stress from accumulating?'
                ],
                'evidence_examples': [
                    'I have specific techniques for vocal rest between calls',
                    'I can maintain consistent empathy and energy across multiple phone interactions',
                    'I know when to take breaks and have strategies for vocal and emotional recovery'
                ]
            }
        ]

        # Combine all questions and create them
        all_questions = questions_data + leaps_questions + resilience_questions
        self._create_questions_with_scale(survey, all_questions)
        self.stdout.write(f'Created Phone survey with {len(all_questions)} questions')

    def create_zoom_benchmark(self, user, category):
        """Create Zoom benchmark with correct structure."""
        survey, created = Survey.objects.get_or_create(
            title='ESSSuper Self-Assessment ZOOM',
            created_by=user,
            defaults={
                'description': '''Self-assessment tool to help staff accurately evaluate their communication skills and identify growth opportunities in virtual interactions.

Duration: 15-20 minutes
Timing: Initial assessment 1-week post-workshop, then monthly for first 3 months, then quarterly.

Scoring: 1-4 scale where 1=Developing, 2=Progressing, 3=Proficient, 4=Advanced
Total possible score: 40 points (10 questions × 4 points each)''',
                'survey_type': 'ZOOM',
                'category': category,
                'enable_scoring': True,
                'enable_ai_analysis': True,
                'is_published': True,
                'scoring_rubric': {
                    'tier_1': {'min': 10, 'max': 19, 'description': 'Tier 1 - Foundation Builder'},
                    'tier_2': {'min': 20, 'max': 29, 'description': 'Tier 2 - Developing Practitioner'},
                    'tier_3': {'min': 30, 'max': 35, 'description': 'Tier 3 - Proficient Communicator'},
                    'tier_4': {'min': 36, 'max': 40, 'description': 'Tier 4 - Expert Practitioner'}
                }
            }
        )

        if not created:
            self.stdout.write(f'Zoom survey already exists: {survey.title}')
            return

        # Section A: Technical Competence & Virtual Presence (3 questions)
        questions_data = [
            {
                'text': 'Camera & Audio Management - Rate your technical skill in creating professional virtual presence',
                'section': 'A. Technical Competence & Virtual Presence',
                'reflection_prompts': [
                    'How do you ensure your camera angle and lighting support professional communication?',
                    'What do you do when technical issues arise during emotional conversations?',
                    'How do you manage your virtual background to support member comfort?'
                ],
                'evidence_examples': [
                    'I test technology before member calls and have backup plans',
                    'My camera is positioned for natural eye contact and professional appearance',
                    'I can troubleshoot common issues without losing member engagement'
                ]
            },
            {
                'text': 'Screen Sharing & Document Management - Rate your ability to use screen sharing effectively during sensitive conversations',
                'section': 'A. Technical Competence & Virtual Presence',
                'reflection_prompts': [
                    'How do you balance screen sharing with maintaining personal connection?',
                    'What do you do when members struggle with technology during document review?',
                    'How do you manage multiple screens/windows while staying present with members?'
                ],
                'evidence_examples': [
                    'I explain screen sharing before starting and check member comfort',
                    'I can navigate documents smoothly while maintaining conversation flow',
                    'I\'m prepared with alternative ways to share information if technology fails'
                ]
            },
            {
                'text': 'Reading Virtual Body Language - Rate your ability to interpret member emotions and responses through video',
                'section': 'A. Technical Competence & Virtual Presence',
                'reflection_prompts': [
                    'What visual cues help you assess member emotional state on video?',
                    'How do you handle when members turn their cameras off during difficult topics?',
                    'What differences do you notice between in-person and video emotional cues?'
                ],
                'evidence_examples': [
                    'I can recognise stress, confusion, or withdrawal through video',
                    'I adjust my approach when I notice member discomfort on camera',
                    'I respect camera preferences while maintaining connection'
                ]
            }
        ]

        # Add LEAPS Framework questions (Section B - 5 questions)
        leaps_questions = [
            {
                'text': 'Listen - Virtual Active Listening - Rate your ability to demonstrate attentive listening through video interaction',
                'section': 'B. LEAPS Framework Application (Zoom Context)',
                'reflection_prompts': [
                    'How do you show active listening when you can only see part of the member?',
                    'What do you do when audio quality makes listening challenging?',
                    'How do you maintain focus when managing multiple technical elements?'
                ],
                'evidence_examples': [
                    'I use verbal acknowledgments more frequently than in-person',
                    'I look at the camera, not the screen, when responding to emotional content',
                    'I ask for clarification when audio issues might affect understanding'
                ]
            },
            {
                'text': 'Empathise - Virtual Empathy Expression - Rate your ability to convey genuine empathy through video interaction',
                'section': 'B. LEAPS Framework Application (Zoom Context)',
                'reflection_prompts': [
                    'How do you ensure your empathy comes through clearly on video?',
                    'What challenges do you face showing genuine care through a screen?',
                    'How do you handle the emotional distance that video can create?'
                ],
                'evidence_examples': [
                    'I use more verbal empathy statements to compensate for video limitations',
                    'My facial expressions are slightly more pronounced to ensure clarity',
                    'I acknowledge the medium when discussing particularly sensitive topics'
                ]
            },
            {
                'text': 'Ask - Sensitive Virtual Questioning - Rate your ability to ask difficult questions compassionately through video',
                'section': 'B. LEAPS Framework Application (Zoom Context)',
                'reflection_prompts': [
                    'How do you make personal questions feel less intrusive on video?',
                    'What do you do when members seem uncomfortable with video questioning?',
                    'How do you handle privacy concerns in virtual environments?'
                ],
                'evidence_examples': [
                    'I\'m more explicit about confidentiality and privacy in virtual settings',
                    'I give extra time for members to process questions on video',
                    'I offer phone alternatives when video feels too invasive'
                ]
            },
            {
                'text': 'Paraphrase - Virtual Understanding Confirmation - Rate your ability to confirm understanding through video interaction',
                'section': 'B. LEAPS Framework Application (Zoom Context)',
                'reflection_prompts': [
                    'How do you ensure your paraphrasing is clearly understood on video?',
                    'What visual or verbal cues tell you when members feel heard through video?',
                    'How do you handle misunderstandings that arise from video communication?'
                ],
                'evidence_examples': [
                    'I use screen sharing or chat to reinforce important paraphrasing',
                    'I\'m more explicit about checking understanding on video',
                    'I summarise more frequently to compensate for video communication gaps'
                ]
            },
            {
                'text': 'Summarise - Virtual Action Planning - Rate your ability to create clear next steps through video collaboration',
                'section': 'B. LEAPS Framework Application (Zoom Context)',
                'reflection_prompts': [
                    'How do you ensure members clearly understand action plans discussed on video?',
                    'What tools do you use to make virtual planning collaborative?',
                    'How do you follow up after video calls to confirm understanding?'
                ],
                'evidence_examples': [
                    'I use screen sharing to create visual action plans during calls',
                    'I send written follow-up summaries after video meetings',
                    'I schedule follow-up calls to check progress on virtual agreements'
                ]
            }
        ]

        # Add Resilience & Self-Care questions (Section C - 2 questions)
        resilience_questions = [
            {
                'text': 'Managing Technology-Related Stress - Rate your ability to stay calm and professional when technology creates additional stress',
                'section': 'C. Virtual Resilience & Technology Stress Management',
                'reflection_prompts': [
                    'How do you handle your own frustration when technology compounds member distress?',
                    'What strategies help you maintain empathy when dealing with technical difficulties?',
                    'How do you prevent technology problems from affecting relationship quality?'
                ],
                'evidence_examples': [
                    'I stay patient and calm when technology creates barriers',
                    'I have backup communication methods ready',
                    'I acknowledge technology frustrations without letting them derail conversations'
                ]
            },
            {
                'text': 'Virtual Boundary Management - Rate your ability to maintain professional boundaries and self-care in virtual environments',
                'section': 'C. Virtual Resilience & Technology Stress Management',
                'reflection_prompts': [
                    'How do you create separation between virtual work interactions and personal space?',
                    'What do you do to recover between back-to-back video calls with distressed members?',
                    'How do you manage the intensity of seeing member distress up close on screen?'
                ],
                'evidence_examples': [
                    'I have rituals for transitioning between virtual member calls',
                    'I take breaks from looking at screens between emotional video calls',
                    'I can maintain professional presence while managing my own virtual fatigue'
                ]
            }
        ]

        # Combine all questions and create them
        all_questions = questions_data + leaps_questions + resilience_questions
        self._create_questions_with_scale(survey, all_questions)
        self.stdout.write(f'Created Zoom survey with {len(all_questions)} questions')

    def _create_questions_with_scale(self, survey, questions_data):
        """Helper method to create questions with 4-point scale."""
        for i, question_data in enumerate(questions_data):
            help_text = f"""
Section: {question_data['section']}

Reflection Prompts:
{chr(10).join('• ' + prompt for prompt in question_data['reflection_prompts'])}

Evidence Examples:
{chr(10).join('• ' + example for example in question_data['evidence_examples'])}
"""

            SurveyQuestion.objects.create(
                survey=survey,
                question_text=question_data['text'],
                question_type='SCALE_CUSTOM',
                order=i + 1,
                weight=1.0,
                is_required=True,
                help_text=help_text.strip(),
                scale_min=1,
                scale_max=4,
                scale_labels={
                    '1': 'Developing - I struggle with this and need significant support',
                    '2': 'Progressing - I understand this but apply it inconsistently',
                    '3': 'Proficient - I consistently apply this skill with confidence',
                    '4': 'Advanced - I master this skill and can guide others'
                }
            )
