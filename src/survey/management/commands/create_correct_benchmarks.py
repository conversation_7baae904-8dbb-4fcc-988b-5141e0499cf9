from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from survey.models import Survey, SurveyQuestion, SurveyQuestionChoice, SurveyCategory
from logger.utils import survey_logger


class Command(BaseCommand):
    help = 'Create correct benchmark surveys based on actual documents with 4-point scale'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=str,
            help='Username to assign as survey creator (defaults to first superuser)',
        )
        parser.add_argument(
            '--replace',
            action='store_true',
            help='Replace existing surveys',
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating correct benchmark survey templates...')

        # Get or create user
        if options['user']:
            try:
                user = User.objects.get(username=options['user'])
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User "{options["user"]}" not found')
                )
                return
        else:
            user = User.objects.filter(is_superuser=True).first()
            if not user:
                self.stdout.write(
                    self.style.ERROR('No superuser found. Please create one first.')
                )
                return

        # Get or create benchmark category
        category, created = SurveyCategory.objects.get_or_create(
            name='ESSSuper Communication Benchmarks',
            defaults={
                'description': 'Self-assessment tools for communication skills evaluation',
                'created_by': user,
                'slug': 'esssuper-communication-benchmarks'
            }
        )

        if options['replace']:
            # Delete existing surveys
            Survey.objects.filter(category=category).delete()
            self.stdout.write('Deleted existing benchmark surveys')

        # Create correct benchmark surveys
        self.create_face_to_face_benchmark(user, category)
        self.create_phone_benchmark(user, category)
        self.create_zoom_benchmark(user, category)

        self.stdout.write(
            self.style.SUCCESS('Successfully created correct benchmark surveys!')
        )

    def create_face_to_face_benchmark(self, user, category):
        """Create Face-to-Face benchmark with correct structure."""
        survey, created = Survey.objects.get_or_create(
            title='ESSSuper Self-Assessment Face to Face',
            created_by=user,
            defaults={
                'description': '''Self-assessment tool to help staff accurately evaluate their communication skills and identify growth opportunities in face-to-face interactions.

Duration: 15-20 minutes
Timing: Initial assessment 1-week post-workshop, then monthly for first 3 months, then quarterly.

Scoring: 1-4 scale where 1=Developing, 2=Progressing, 3=Proficient, 4=Advanced
Total possible score: 40 points (10 questions × 4 points each)''',
                'survey_type': 'FACE_TO_FACE',
                'category': category,
                'enable_scoring': True,
                'enable_ai_analysis': True,
                'is_published': True,
                'scoring_rubric': {
                    'tier_1': {'min': 10, 'max': 19, 'description': 'Tier 1 - Foundation Builder'},
                    'tier_2': {'min': 20, 'max': 29, 'description': 'Tier 2 - Developing Practitioner'},
                    'tier_3': {'min': 30, 'max': 35, 'description': 'Tier 3 - Proficient Communicator'},
                    'tier_4': {'min': 36, 'max': 40, 'description': 'Tier 4 - Expert Practitioner'}
                }
            }
        )

        if not created:
            self.stdout.write(f'Face-to-Face survey already exists: {survey.title}')
            return

        # Section A: Professional Presence & Environment Management (3 questions)
        questions_data = [
            {
                'text': 'Physical Positioning & Space - Rate your ability to create a welcoming, professional physical environment',
                'section': 'A. Professional Presence & Environment Management',
                'reflection_prompts': [
                    'How you typically arrange seating for member meetings',
                    'What do you do when a member seems uncomfortable with the physical space?',
                    'How do you maintain appropriate professional distance while showing warmth?'
                ],
                'evidence_examples': [
                    'I consistently position myself at an angle rather than directly across from members',
                    'I offer water/tissues proactively and have them easily accessible',
                    'I adjust lighting or seating when I notice member discomfort'
                ]
            },
            {
                'text': 'Body Language & Non-Verbal Communication - Rate your awareness and control of your own body language during member interactions',
                'section': 'A. Professional Presence & Environment Management',
                'reflection_prompts': [
                    'How do you know when your body language is communicating empathy vs. defensiveness?',
                    'Describe a time when you consciously adjusted your posture during a difficult conversation',
                    'What physical habits do you have that might distract from member communication?'
                ],
                'evidence_examples': [
                    'I maintain open posture even when discussing difficult topics',
                    'I notice and adjust when I cross my arms or lean back',
                    'I use forward lean and nodding to show active engagement'
                ]
            },
            {
                'text': 'Managing Visual Distress - Rate your ability to respond appropriately when members show visible emotional distress',
                'section': 'A. Professional Presence & Environment Management',
                'reflection_prompts': [
                    'How do you handle your own emotional response when a member becomes tearful?',
                    'What do you do when a member\'s body language suggests they\'re overwhelmed?',
                    'How do you maintain professional boundaries while showing genuine care?'
                ],
                'evidence_examples': [
                    'I remain calm and grounded when members show strong emotions',
                    'I offer appropriate comfort (tissues, brief pause) without overstepping',
                    'I can continue the conversation professionally while acknowledging distress'
                ]
            }
        ]

        # Add LEAPS Framework questions (Section B - 5 questions)
        leaps_questions = [
            {
                'text': 'Listen - Visual Active Listening - Rate your ability to demonstrate attentive listening through body language and visual cues',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do members typically respond to your listening style?',
                    'What visual cues tell you that you\'re truly hearing what a member is saying?',
                    'When do you find it hardest to maintain active listening posture?'
                ],
                'evidence_examples': [
                    'I maintain appropriate eye contact without staring',
                    'I use subtle nodding and facial expressions to encourage sharing',
                    'I avoid looking at papers/screens when members are speaking emotionally'
                ]
            },
            {
                'text': 'Empathise - Visual Empathy Demonstration - Rate your ability to show genuine understanding through facial expressions and presence',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do you show empathy without appearing overly emotional yourself?',
                    'Describe how your facial expressions change when discussing difficult topics',
                    'What helps you maintain genuine empathy during repetitive or frustrating conversations?'
                ],
                'evidence_examples': [
                    'My facial expressions naturally reflect appropriate concern',
                    'I can show understanding without taking on the member\'s emotional state',
                    'Members often comment that they feel heard and understood'
                ]
            },
            {
                'text': 'Ask - Sensitive Questioning Techniques - Rate your ability to ask clarifying questions while maintaining member dignity',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do you approach asking personal questions about finances or health?',
                    'What techniques do you use to make invasive questions feel less intrusive?',
                    'How do you handle when members become defensive about your questions?'
                ],
                'evidence_examples': [
                    'I preface sensitive questions with empathetic context',
                    'I explain why information is needed before asking for it',
                    'I give members time to process questions before expecting answers'
                ]
            },
            {
                'text': 'Paraphrase - Confirming Understanding Visually - Rate your ability to reflect back what you\'ve heard while reading member responses',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do you know when your paraphrasing has accurately captured the member\'s concern?',
                    'What visual cues from members tell you they feel understood vs. misunderstood?',
                    'How do you handle when your paraphrase doesn\'t match their intended meaning?'
                ],
                'evidence_examples': [
                    'I watch for nods, relief, or continued tension when I paraphrase',
                    'I invite correction if I haven\'t captured their meaning accurately',
                    'I use their emotional language, not just the factual content'
                ]
            },
            {
                'text': 'Summarise - Clear Resolution Planning - Rate your ability to create clear, collaborative next steps while maintaining visual engagement',
                'section': 'B. LEAPS Framework Application (Face-to-Face Context)',
                'reflection_prompts': [
                    'How do you ensure members understand and agree with next steps?',
                    'What do you do when a member\'s body language suggests confusion about the plan?',
                    'How do you balance thoroughness with not overwhelming members?'
                ],
                'evidence_examples': [
                    'I use written notes or diagrams to reinforce verbal summaries',
                    'I check for understanding by watching facial expressions, not just asking',
                    'I provide take-away materials that reinforce our conversation'
                ]
            }
        ]

        # Add Resilience & Self-Care questions (Section C - 2 questions)
        resilience_questions = [
            {
                'text': 'Managing Your Own Emotional Response - Rate your ability to maintain professional composure while processing member distress',
                'section': 'C. Resilience & Self-Care in Face-to-Face Settings',
                'reflection_prompts': [
                    'What physical sensations tell you when you\'re becoming emotionally affected?',
                    'How do you reset between difficult face-to-face appointments?',
                    'What strategies help you maintain appropriate emotional boundaries?'
                ],
                'evidence_examples': [
                    'I can feel my own stress rising and take subtle steps to manage it',
                    'I use breathing techniques that don\'t distract from the member',
                    'I debrief with colleagues when needed without breaking confidentiality'
                ]
            },
            {
                'text': 'Post-Interaction Recovery - Rate your effectiveness at processing and recovering from challenging face-to-face interactions',
                'section': 'C. Resilience & Self-Care in Face-to-Face Settings',
                'reflection_prompts': [
                    'What do you do immediately after a particularly difficult member meeting?',
                    'How do you prevent one difficult interaction from affecting the next?',
                    'What signs tell you when you need additional support after member meetings?'
                ],
                'evidence_examples': [
                    'I have a consistent routine for transitioning between member appointments',
                    'I know when to seek supervisor support vs. handling independently',
                    'I can identify my stress triggers and have specific coping strategies'
                ]
            }
        ]

        # Combine all questions
        all_questions = questions_data + leaps_questions + resilience_questions

        # Create questions with 4-point scale
        for i, question_data in enumerate(all_questions):
            help_text = f"""
Section: {question_data['section']}

Reflection Prompts:
{chr(10).join('• ' + prompt for prompt in question_data['reflection_prompts'])}

Evidence Examples:
{chr(10).join('• ' + example for example in question_data['evidence_examples'])}
"""
            
            question = SurveyQuestion.objects.create(
                survey=survey,
                question_text=question_data['text'],
                question_type='SCALE_CUSTOM',
                order=i + 1,
                weight=1.0,
                is_required=True,
                help_text=help_text.strip(),
                scale_min=1,
                scale_max=4,
                scale_labels={
                    '1': 'Developing - I struggle with this and need significant support',
                    '2': 'Progressing - I understand this but apply it inconsistently', 
                    '3': 'Proficient - I consistently apply this skill with confidence',
                    '4': 'Advanced - I master this skill and can guide others'
                }
            )

        self.stdout.write(f'Created Face-to-Face survey with {len(all_questions)} questions')

    def create_phone_benchmark(self, user, category):
        """Create Phone benchmark - similar structure, different content."""
        # Implementation would follow same pattern with phone-specific questions
        self.stdout.write('Phone benchmark creation - implement similar to face-to-face')

    def create_zoom_benchmark(self, user, category):
        """Create Zoom benchmark - similar structure, different content."""
        # Implementation would follow same pattern with zoom-specific questions  
        self.stdout.write('Zoom benchmark creation - implement similar to face-to-face')
