from django.core.management.base import BaseCommand
from django.utils import timezone
from survey.models import SurveySession
from survey.ai_analysis import SurveyAIAnalyzer
from logger.utils import survey_logger
import time


class Command(BaseCommand):
    help = 'Run AI analysis on completed survey sessions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--session-id',
            type=str,
            help='Analyze specific session by ID',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=10,
            help='Number of sessions to process in one batch (default: 10)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Re-analyze sessions that already have AI analysis',
        )
        parser.add_argument(
            '--survey-type',
            type=str,
            choices=['FACE_TO_FACE', 'PHONE', 'ZOOM', 'CUSTOM'],
            help='Only analyze sessions of specific survey type',
        )

    def handle(self, *args, **options):
        self.stdout.write('Starting AI analysis of survey sessions...')
        
        analyzer = SurveyAIAnalyzer()
        
        # Get sessions to analyze
        if options['session_id']:
            sessions = SurveySession.objects.filter(id=options['session_id'])
            if not sessions.exists():
                self.stdout.write(
                    self.style.ERROR(f'Session with ID {options["session_id"]} not found')
                )
                return
        else:
            # Get completed sessions without AI analysis (or force re-analysis)
            sessions = SurveySession.objects.filter(is_complete=True)
            
            if not options['force']:
                sessions = sessions.filter(ai_analysis_completed=False)
            
            if options['survey_type']:
                sessions = sessions.filter(survey__survey_type=options['survey_type'])
            
            # Limit batch size
            sessions = sessions[:options['batch_size']]
        
        if not sessions.exists():
            self.stdout.write(
                self.style.WARNING('No sessions found for AI analysis')
            )
            return
        
        self.stdout.write(f'Found {sessions.count()} sessions to analyze')
        
        success_count = 0
        error_count = 0
        
        for session in sessions:
            try:
                self.stdout.write(f'Analyzing session {session.id}...')
                
                # Check if survey has AI analysis enabled
                if not session.survey.enable_ai_analysis:
                    self.stdout.write(
                        self.style.WARNING(
                            f'Skipping session {session.id} - AI analysis disabled for survey'
                        )
                    )
                    continue
                
                # Run analysis
                start_time = time.time()
                analysis_result = analyzer.analyze_session(session)
                end_time = time.time()
                
                # Log success
                survey_logger.info(
                    f'AI analysis completed for session {session.id}',
                    user=session.user,
                    extra_data={
                        'session_id': str(session.id),
                        'survey_title': session.survey.title,
                        'analysis_time': f'{end_time - start_time:.2f}s',
                        'overall_score': analysis_result.get('overall_score'),
                        'performance_level': analysis_result.get('performance_level')
                    }
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✓ Session {session.id} analyzed successfully '
                        f'(Score: {analysis_result.get("overall_score", 0):.1f}%, '
                        f'Level: {analysis_result.get("performance_level", "Unknown")})'
                    )
                )
                
                success_count += 1
                
                # Small delay to avoid rate limiting
                time.sleep(1)
                
            except Exception as e:
                error_count += 1
                
                survey_logger.error(
                    f'AI analysis failed for session {session.id}: {str(e)}',
                    user=session.user,
                    extra_data={
                        'session_id': str(session.id),
                        'error': str(e)
                    }
                )
                
                self.stdout.write(
                    self.style.ERROR(
                        f'✗ Failed to analyze session {session.id}: {str(e)}'
                    )
                )
                
                # Continue with next session
                continue
        
        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(f'AI Analysis Summary:')
        self.stdout.write(f'  Total sessions processed: {success_count + error_count}')
        self.stdout.write(
            self.style.SUCCESS(f'  Successful analyses: {success_count}')
        )
        if error_count > 0:
            self.stdout.write(
                self.style.ERROR(f'  Failed analyses: {error_count}')
            )
        self.stdout.write('='*50)
        
        if success_count > 0:
            self.stdout.write(
                self.style.SUCCESS('AI analysis completed successfully!')
            )
        elif error_count > 0:
            self.stdout.write(
                self.style.ERROR('AI analysis completed with errors.')
            )
        else:
            self.stdout.write(
                self.style.WARNING('No sessions were processed.')
            )
