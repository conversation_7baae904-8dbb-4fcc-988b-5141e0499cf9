from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils.text import slugify
from survey.models import Survey, SurveyQuestion, SurveyQuestionChoice, SurveyCategory
from logger.utils import survey_logger


class Command(BaseCommand):
    help = 'Create benchmark survey templates based on reference documents'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=str,
            help='Username to assign as survey creator (defaults to first superuser)',
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating benchmark survey templates...')

        # Get or create user
        if options['user']:
            try:
                user = User.objects.get(username=options['user'])
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User "{options["user"]}" not found')
                )
                return
        else:
            user = User.objects.filter(is_superuser=True).first()
            if not user:
                self.stdout.write(
                    self.style.ERROR('No superuser found. Please create one first.')
                )
                return

        # Create or get benchmark category
        category, created = SurveyCategory.objects.get_or_create(
            name='Communication Benchmarks',
            defaults={
                'description': 'Standardized communication assessment surveys',
                'created_by': user,
                'slug': 'communication-benchmarks'
            }
        )

        if created:
            self.stdout.write(f'Created category: {category.name}')

        # Create benchmark surveys
        self.create_face_to_face_benchmark(user, category)
        self.create_phone_benchmark(user, category)
        self.create_zoom_benchmark(user, category)

        self.stdout.write(
            self.style.SUCCESS('Successfully created all benchmark surveys!')
        )

    def create_face_to_face_benchmark(self, user, category):
        """Create Face-to-Face Communication Benchmark survey."""
        survey, created = Survey.objects.get_or_create(
            title='Face-to-Face Communication Benchmark',
            created_by=user,
            defaults={
                'description': '''Comprehensive assessment of face-to-face communication skills including verbal clarity, 
                non-verbal communication, engagement, and overall effectiveness. This benchmark evaluates in-person 
                interaction capabilities across multiple dimensions.''',
                'survey_type': 'FACE_TO_FACE',
                'category': category,
                'enable_scoring': True,
                'enable_ai_analysis': True,
                'scoring_rubric': {
                    'excellent': {'min': 80, 'max': 100, 'description': 'Outstanding communication skills'},
                    'good': {'min': 60, 'max': 79, 'description': 'Strong communication abilities'},
                    'satisfactory': {'min': 40, 'max': 59, 'description': 'Adequate communication skills'},
                    'needs_improvement': {'min': 20, 'max': 39, 'description': 'Communication skills need development'},
                    'poor': {'min': 0, 'max': 19, 'description': 'Significant communication challenges'}
                },
                'ai_analysis_prompt': '''Analyze face-to-face communication performance focusing on:
                1. Verbal communication clarity and effectiveness
                2. Non-verbal communication and body language
                3. Eye contact and engagement levels
                4. Overall interpersonal skills
                Provide specific recommendations for improvement.'''
            }
        )

        if not created:
            self.stdout.write(f'Face-to-Face survey already exists: {survey.title}')
            return

        questions_data = [
            {
                'text': 'How would you rate the overall clarity of verbal communication?',
                'type': 'LIKERT_5',
                'weight': 2.0,
                'help_text': 'Consider pronunciation, volume, pace, and articulation'
            },
            {
                'text': 'How effective was the non-verbal communication (body language, gestures, facial expressions)?',
                'type': 'LIKERT_5',
                'weight': 2.0,
                'help_text': 'Evaluate appropriateness and supportiveness of non-verbal cues'
            },
            {
                'text': 'How well did the speaker maintain appropriate eye contact?',
                'type': 'LIKERT_5',
                'weight': 1.5,
                'help_text': 'Consider frequency, duration, and cultural appropriateness'
            },
            {
                'text': 'Rate the level of engagement and active listening demonstrated.',
                'type': 'LIKERT_7',
                'weight': 1.5,
                'help_text': 'Look for responsiveness, questions, and interaction quality'
            },
            {
                'text': 'How appropriate was the physical positioning and use of personal space?',
                'type': 'LIKERT_5',
                'weight': 1.0,
                'help_text': 'Consider cultural norms and comfort levels'
            },
            {
                'text': 'Was the pace of conversation appropriate for the context?',
                'type': 'YES_NO',
                'weight': 1.0,
                'help_text': 'Consider if speaking speed allowed for comprehension'
            },
            {
                'text': 'Rate the overall confidence and presence displayed.',
                'type': 'RATING_10',
                'weight': 1.5,
                'help_text': 'Scale: 1=Very Low Confidence, 10=Very High Confidence'
            },
            {
                'text': 'How well did the speaker adapt their communication style to the audience?',
                'type': 'LIKERT_5',
                'weight': 1.5,
                'help_text': 'Consider audience awareness and style flexibility'
            },
            {
                'text': 'Additional observations or specific examples of effective/ineffective communication:',
                'type': 'TEXT_LONG',
                'weight': 0.5,
                'required': False,
                'help_text': 'Provide specific examples and constructive feedback'
            }
        ]

        self.create_questions(survey, questions_data)
        self.stdout.write(f'Created Face-to-Face survey with {len(questions_data)} questions')

    def create_phone_benchmark(self, user, category):
        """Create Phone Communication Benchmark survey."""
        survey, created = Survey.objects.get_or_create(
            title='Phone Communication Benchmark',
            created_by=user,
            defaults={
                'description': '''Assessment of phone communication skills focusing on vocal clarity, 
                phone etiquette, and audio-only interaction effectiveness. Evaluates ability to 
                communicate effectively without visual cues.''',
                'survey_type': 'PHONE',
                'category': category,
                'enable_scoring': True,
                'enable_ai_analysis': True,
                'scoring_rubric': {
                    'excellent': {'min': 85, 'max': 100, 'description': 'Exceptional phone communication'},
                    'good': {'min': 65, 'max': 84, 'description': 'Strong phone skills'},
                    'satisfactory': {'min': 45, 'max': 64, 'description': 'Adequate phone communication'},
                    'needs_improvement': {'min': 25, 'max': 44, 'description': 'Phone skills need work'},
                    'poor': {'min': 0, 'max': 24, 'description': 'Poor phone communication'}
                },
                'ai_analysis_prompt': '''Analyze phone communication performance focusing on:
                1. Vocal clarity and audio quality
                2. Phone etiquette and professionalism
                3. Effectiveness without visual cues
                4. Call management and structure
                Provide recommendations for phone communication improvement.'''
            }
        )

        if not created:
            self.stdout.write(f'Phone survey already exists: {survey.title}')
            return

        questions_data = [
            {
                'text': 'How clear was the audio quality and connection throughout the call?',
                'type': 'LIKERT_5',
                'weight': 2.0,
                'help_text': 'Consider technical quality and any disruptions'
            },
            {
                'text': 'Rate the speaker\'s vocal clarity, articulation, and pronunciation.',
                'type': 'LIKERT_7',
                'weight': 2.5,
                'help_text': 'Focus on how well words were understood'
            },
            {
                'text': 'How professional was the phone etiquette and greeting?',
                'type': 'LIKERT_5',
                'weight': 1.5,
                'help_text': 'Consider opening, closing, and overall phone manners'
            },
            {
                'text': 'Was the speaking pace appropriate for phone communication?',
                'type': 'YES_NO',
                'weight': 1.0,
                'help_text': 'Phone communication often requires slower pace'
            },
            {
                'text': 'How effectively did the speaker use vocal variety (tone, pitch, emphasis)?',
                'type': 'LIKERT_5',
                'weight': 1.5,
                'help_text': 'Important for maintaining engagement without visuals'
            },
            {
                'text': 'Rate the call structure and organization.',
                'type': 'RATING_10',
                'weight': 1.5,
                'help_text': 'Scale: 1=Very Disorganized, 10=Very Well Structured'
            },
            {
                'text': 'How well did the speaker confirm understanding and check for questions?',
                'type': 'LIKERT_5',
                'weight': 1.5,
                'help_text': 'Critical for phone communication effectiveness'
            },
            {
                'text': 'Was the call duration appropriate for the content discussed?',
                'type': 'YES_NO',
                'weight': 1.0,
                'help_text': 'Consider efficiency and thoroughness balance'
            },
            {
                'text': 'Comments on phone communication strengths and areas for improvement:',
                'type': 'TEXT_LONG',
                'weight': 0.5,
                'required': False,
                'help_text': 'Specific feedback on phone-specific communication skills'
            }
        ]

        self.create_questions(survey, questions_data)
        self.stdout.write(f'Created Phone survey with {len(questions_data)} questions')

    def create_zoom_benchmark(self, user, category):
        """Create Zoom/Video Conference Benchmark survey."""
        survey, created = Survey.objects.get_or_create(
            title='Video Conference (Zoom) Communication Benchmark',
            created_by=user,
            defaults={
                'description': '''Comprehensive assessment of video conferencing communication skills including 
                technical proficiency, virtual presence, and digital interaction effectiveness. Evaluates 
                adaptation to virtual meeting environments.''',
                'survey_type': 'ZOOM',
                'category': category,
                'enable_scoring': True,
                'enable_ai_analysis': True,
                'scoring_rubric': {
                    'excellent': {'min': 80, 'max': 100, 'description': 'Outstanding virtual communication'},
                    'good': {'min': 60, 'max': 79, 'description': 'Strong video conference skills'},
                    'satisfactory': {'min': 40, 'max': 59, 'description': 'Adequate virtual presence'},
                    'needs_improvement': {'min': 20, 'max': 39, 'description': 'Virtual skills need development'},
                    'poor': {'min': 0, 'max': 19, 'description': 'Poor video conference communication'}
                },
                'ai_analysis_prompt': '''Analyze video conference communication performance focusing on:
                1. Technical setup and video/audio quality
                2. Virtual presence and engagement
                3. Use of video conferencing features
                4. Adaptation to digital communication medium
                Provide specific recommendations for virtual communication improvement.'''
            }
        )

        if not created:
            self.stdout.write(f'Zoom survey already exists: {survey.title}')
            return

        questions_data = [
            {
                'text': 'How was the video quality and camera setup throughout the session?',
                'type': 'LIKERT_5',
                'weight': 1.5,
                'help_text': 'Consider resolution, stability, and framing'
            },
            {
                'text': 'Rate the audio quality and clarity.',
                'type': 'LIKERT_5',
                'weight': 2.0,
                'help_text': 'Include microphone quality and background noise'
            },
            {
                'text': 'How appropriate was the lighting and camera positioning?',
                'type': 'LIKERT_5',
                'weight': 1.0,
                'help_text': 'Consider visibility and professional appearance'
            },
            {
                'text': 'How effectively did the presenter use video conferencing features (screen share, chat, etc.)?',
                'type': 'LIKERT_7',
                'weight': 1.5,
                'help_text': 'Evaluate technical proficiency and feature utilization'
            },
            {
                'text': 'Was the virtual background or environment appropriate and professional?',
                'type': 'YES_NO',
                'weight': 1.0,
                'help_text': 'Consider background choice and professionalism'
            },
            {
                'text': 'Rate the level of engagement and eye contact with the camera.',
                'type': 'LIKERT_5',
                'weight': 2.0,
                'help_text': 'Virtual eye contact is crucial for engagement'
            },
            {
                'text': 'How well did the speaker manage virtual meeting etiquette (muting, turn-taking, etc.)?',
                'type': 'LIKERT_5',
                'weight': 1.5,
                'help_text': 'Consider digital meeting best practices'
            },
            {
                'text': 'Rate the overall virtual presence and energy level.',
                'type': 'RATING_10',
                'weight': 2.0,
                'help_text': 'Scale: 1=Very Low Energy, 10=Very High Energy'
            },
            {
                'text': 'How effectively did the speaker encourage participation and interaction?',
                'type': 'LIKERT_5',
                'weight': 1.5,
                'help_text': 'Important for virtual meeting success'
            },
            {
                'text': 'Specific feedback on virtual communication skills and technical setup:',
                'type': 'TEXT_LONG',
                'weight': 0.5,
                'required': False,
                'help_text': 'Include both technical and communication observations'
            }
        ]

        self.create_questions(survey, questions_data)
        self.stdout.write(f'Created Zoom survey with {len(questions_data)} questions')

    def create_questions(self, survey, questions_data):
        """Create questions for a survey."""
        for i, question_data in enumerate(questions_data):
            question = SurveyQuestion.objects.create(
                survey=survey,
                question_text=question_data['text'],
                question_type=question_data['type'],
                order=i + 1,
                weight=question_data.get('weight', 1.0),
                is_required=question_data.get('required', True),
                help_text=question_data.get('help_text', '')
            )

            # Create choices for multiple choice questions if needed
            if question_data['type'] == 'MULTIPLE_CHOICE' and 'choices' in question_data:
                for j, choice_data in enumerate(question_data['choices']):
                    SurveyQuestionChoice.objects.create(
                        question=question,
                        text=choice_data['text'],
                        value=choice_data['value'],
                        order=j + 1
                    )

        survey_logger.info(f'Created {len(questions_data)} questions for survey: {survey.title}')
