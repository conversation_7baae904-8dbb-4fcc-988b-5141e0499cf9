from django.utils import timezone
from django.db import transaction
from .models import SurveySession, SurveyResponse, SurveyQuestion, SurveyQuestionChoice
from .validators import SurveyResponseValidator
from logger.utils import survey_logger, log_survey_event
import random


class SurveySessionManager:
    """Utility class for managing survey sessions."""
    
    def __init__(self, survey, user=None, session_key=None, request=None):
        self.survey = survey
        self.user = user
        self.session_key = session_key
        self.request = request
    
    def create_session(self):
        """Create a new survey session."""
        # Check if user can take survey
        if not self.survey.allow_anonymous and not self.user:
            raise ValueError("This survey requires authentication.")
        
        if not self.survey.multiple_responses and self.user:
            existing_session = SurveySession.objects.filter(
                survey=self.survey,
                user=self.user,
                is_complete=True
            ).first()
            if existing_session:
                raise ValueError("You have already completed this survey.")
        
        # Get questions in order
        questions = list(self.survey.questions.filter(is_active=True).order_by('order'))
        
        if not questions:
            raise ValueError("This survey has no active questions.")
        
        # Randomize order if needed
        if self.survey.random_order:
            random.shuffle(questions)
        
        question_order = [q.id for q in questions]
        
        # Create session
        session_data = {
            'survey': self.survey,
            'question_order': question_order,
            'current_question_index': 0,
        }
        
        if self.user:
            session_data['user'] = self.user
        else:
            session_data['session_key'] = self.session_key or 'anonymous'
        
        # Add request metadata if available
        if self.request:
            session_data['ip_address'] = self.get_client_ip()
            session_data['user_agent'] = self.request.META.get('HTTP_USER_AGENT', '')
        
        session = SurveySession.objects.create(**session_data)
        
        # Log event
        log_survey_event('session_started', self.survey.id, self.user, {
            'session_id': str(session.id),
            'question_count': len(question_order),
            'is_anonymous': not bool(self.user)
        })
        
        return session
    
    def get_client_ip(self):
        """Get client IP address from request."""
        if not self.request:
            return None
        
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


class SurveyResponseHandler:
    """Utility class for handling survey responses."""
    
    def __init__(self, session):
        self.session = session
    
    @transaction.atomic
    def save_response(self, question, response_data):
        """Save a response to a survey question."""
        # Validate response
        validator = SurveyResponseValidator(question)
        
        # Determine response value based on question type
        if question.question_type in ['LIKERT_5', 'LIKERT_7', 'RATING_10', 'SCALE_CUSTOM', 'YES_NO']:
            numeric_value = response_data.get('numeric_value')
            validator.validate_response(numeric_value)
            
            response, created = SurveyResponse.objects.update_or_create(
                session=self.session,
                question=question,
                defaults={
                    'numeric_value': numeric_value,
                    'responded_at': timezone.now()
                }
            )
        
        elif question.question_type == 'MULTIPLE_CHOICE':
            choice_id = response_data.get('choice_id')
            validator.validate_response(choice_id)
            
            try:
                choice = SurveyQuestionChoice.objects.get(id=choice_id, question=question)
                response, created = SurveyResponse.objects.update_or_create(
                    session=self.session,
                    question=question,
                    defaults={
                        'choice_value': choice,
                        'numeric_value': choice.value,  # Store numeric value for scoring
                        'responded_at': timezone.now()
                    }
                )
            except SurveyQuestionChoice.DoesNotExist:
                raise ValueError("Invalid choice selected.")
        
        elif question.question_type in ['TEXT_SHORT', 'TEXT_LONG']:
            text_value = response_data.get('text_value', '').strip()
            validator.validate_response(text_value)
            
            response, created = SurveyResponse.objects.update_or_create(
                session=self.session,
                question=question,
                defaults={
                    'text_value': text_value,
                    'responded_at': timezone.now()
                }
            )
        
        else:
            raise ValueError(f"Unsupported question type: {question.question_type}")
        
        # Calculate score for this response
        response.calculate_score()
        response.save()
        
        # Log response
        log_survey_event('response_saved', self.session.survey.id, self.session.user, {
            'session_id': str(self.session.id),
            'question_id': question.id,
            'question_type': question.question_type,
            'is_new_response': created
        })
        
        return response
    
    def advance_session(self):
        """Move to the next question in the session."""
        self.session.current_question_index += 1
        self.session.save()
        
        # Check if session is complete
        if self.session.current_question_index >= len(self.session.question_order):
            self.complete_session()
            return True  # Session completed
        
        return False  # More questions remaining
    
    def complete_session(self):
        """Complete the survey session."""
        self.session.complete_session()
        
        # Log completion
        log_survey_event('session_completed', self.session.survey.id, self.session.user, {
            'session_id': str(self.session.id),
            'total_score': self.session.total_score,
            'completion_time': str(self.session.time_spent) if self.session.time_spent else None
        })
        
        # Update survey analytics
        if hasattr(self.session.survey, 'analytics'):
            self.session.survey.analytics.update_analytics()

        # Trigger AI analysis if enabled
        if self.session.survey.enable_ai_analysis:
            self._trigger_ai_analysis()

    def _trigger_ai_analysis(self):
        """Trigger AI analysis for the completed session."""
        try:
            from .ai_analysis import SurveyAIAnalyzer
            analyzer = SurveyAIAnalyzer()
            analyzer.analyze_session(self.session)
        except Exception as e:
            # Log error but don't fail the completion
            log_survey_event('ai_analysis_failed', self.session.survey.id, self.session.user, {
                'session_id': str(self.session.id),
                'error': str(e)
            })


class SurveyQuestionRenderer:
    """Utility class for rendering different question types."""
    
    @staticmethod
    def get_question_context(question, existing_response=None):
        """Get context data for rendering a question."""
        context = {
            'question': question,
            'existing_response': existing_response,
            'question_type': question.question_type,
            'is_required': question.is_required,
            'help_text': question.help_text,
        }
        
        if question.question_type == 'LIKERT_5':
            context['scale_options'] = [
                {'value': 1, 'label': '1 - Strongly Disagree'},
                {'value': 2, 'label': '2 - Disagree'},
                {'value': 3, 'label': '3 - Neutral'},
                {'value': 4, 'label': '4 - Agree'},
                {'value': 5, 'label': '5 - Strongly Agree'},
            ]
        
        elif question.question_type == 'LIKERT_7':
            context['scale_options'] = [
                {'value': 1, 'label': '1 - Strongly Disagree'},
                {'value': 2, 'label': '2 - Disagree'},
                {'value': 3, 'label': '3 - Somewhat Disagree'},
                {'value': 4, 'label': '4 - Neutral'},
                {'value': 5, 'label': '5 - Somewhat Agree'},
                {'value': 6, 'label': '6 - Agree'},
                {'value': 7, 'label': '7 - Strongly Agree'},
            ]
        
        elif question.question_type == 'RATING_10':
            context['scale_options'] = [
                {'value': i, 'label': str(i)} for i in range(1, 11)
            ]
        
        elif question.question_type == 'SCALE_CUSTOM':
            if question.scale_min and question.scale_max:
                options = []
                for i in range(question.scale_min, question.scale_max + 1):
                    label = question.scale_labels.get(str(i), str(i))
                    # For ESSSuper surveys, use the full label text
                    options.append({'value': i, 'label': label})
                context['scale_options'] = options
        
        elif question.question_type == 'MULTIPLE_CHOICE':
            context['choices'] = question.choices.all().order_by('order')
        
        elif question.question_type == 'YES_NO':
            context['yes_no_options'] = [
                {'value': 1, 'label': 'Yes'},
                {'value': 0, 'label': 'No'},
            ]
        
        # Add existing response value for pre-filling
        if existing_response:
            if existing_response.numeric_value is not None:
                context['selected_value'] = existing_response.numeric_value
            elif existing_response.choice_value:
                context['selected_choice'] = existing_response.choice_value.id
            elif existing_response.text_value:
                context['text_value'] = existing_response.text_value
        
        return context


def create_benchmark_survey_template(survey_type, user, category):
    """Create a survey template based on benchmark type."""
    from .models import Survey, SurveyQuestion
    
    templates = {
        'FACE_TO_FACE': {
            'title': 'Face-to-Face Communication Benchmark',
            'description': 'Assess face-to-face communication skills and effectiveness.',
            'questions': [
                {
                    'text': 'How would you rate the clarity of verbal communication?',
                    'type': 'LIKERT_5',
                    'weight': 1.5
                },
                {
                    'text': 'How effective was the non-verbal communication (body language, gestures)?',
                    'type': 'LIKERT_5',
                    'weight': 1.5
                },
                {
                    'text': 'How well did the speaker maintain eye contact?',
                    'type': 'LIKERT_5',
                    'weight': 1.0
                },
                {
                    'text': 'Rate the overall engagement level during the conversation.',
                    'type': 'RATING_10',
                    'weight': 2.0
                },
                {
                    'text': 'Was the pace of conversation appropriate?',
                    'type': 'YES_NO',
                    'weight': 1.0
                },
                {
                    'text': 'Additional comments or observations:',
                    'type': 'TEXT_LONG',
                    'weight': 0.5,
                    'required': False
                }
            ]
        },
        'PHONE': {
            'title': 'Phone Communication Benchmark',
            'description': 'Evaluate phone communication skills and call quality.',
            'questions': [
                {
                    'text': 'How clear was the audio quality during the call?',
                    'type': 'LIKERT_5',
                    'weight': 2.0
                },
                {
                    'text': 'Rate the speaker\'s vocal clarity and articulation.',
                    'type': 'LIKERT_7',
                    'weight': 1.5
                },
                {
                    'text': 'How professional was the phone etiquette?',
                    'type': 'RATING_10',
                    'weight': 1.5
                },
                {
                    'text': 'Was the call duration appropriate for the topic discussed?',
                    'type': 'YES_NO',
                    'weight': 1.0
                },
                {
                    'text': 'Rate the overall effectiveness of the phone conversation.',
                    'type': 'LIKERT_5',
                    'weight': 2.0
                }
            ]
        },
        'ZOOM': {
            'title': 'Video Conference (Zoom) Benchmark',
            'description': 'Assess video conferencing communication and technical proficiency.',
            'questions': [
                {
                    'text': 'How was the video quality throughout the session?',
                    'type': 'LIKERT_5',
                    'weight': 1.5
                },
                {
                    'text': 'Rate the audio quality and clarity.',
                    'type': 'LIKERT_5',
                    'weight': 1.5
                },
                {
                    'text': 'How effectively did the presenter use video conferencing features?',
                    'type': 'LIKERT_7',
                    'weight': 1.0
                },
                {
                    'text': 'Was the lighting and camera positioning appropriate?',
                    'type': 'YES_NO',
                    'weight': 1.0
                },
                {
                    'text': 'Rate the level of engagement and interaction.',
                    'type': 'RATING_10',
                    'weight': 2.0
                },
                {
                    'text': 'How would you rate the overall virtual meeting experience?',
                    'type': 'LIKERT_5',
                    'weight': 2.0
                }
            ]
        }
    }
    
    if survey_type not in templates:
        raise ValueError(f"Unknown survey type: {survey_type}")
    
    template = templates[survey_type]
    
    # Create survey
    survey = Survey.objects.create(
        title=template['title'],
        description=template['description'],
        survey_type=survey_type,
        category=category,
        created_by=user,
        enable_scoring=True,
        enable_ai_analysis=True
    )
    
    # Create questions
    for i, question_data in enumerate(template['questions']):
        SurveyQuestion.objects.create(
            survey=survey,
            question_text=question_data['text'],
            question_type=question_data['type'],
            order=i + 1,
            weight=question_data.get('weight', 1.0),
            is_required=question_data.get('required', True)
        )
    
    return survey
