from django import forms
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from .models import Survey, SurveyQuestion, SurveyQuestionChoice, SurveyResponse, SurveyCategory


class SurveyForm(forms.ModelForm):
    """Form for creating and editing surveys."""
    
    class Meta:
        model = Survey
        fields = [
            'title', 'description', 'survey_type', 'category',
            'random_order', 'allow_anonymous', 'multiple_responses',
            'enable_scoring', 'enable_ai_analysis', 'ai_analysis_prompt'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'input input-bordered w-full',
                'placeholder': 'Enter survey title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'textarea textarea-bordered w-full',
                'rows': 4,
                'placeholder': 'Describe your survey'
            }),
            'survey_type': forms.Select(attrs={
                'class': 'select select-bordered w-full'
            }),
            'category': forms.Select(attrs={
                'class': 'select select-bordered w-full'
            }),
            'ai_analysis_prompt': forms.Textarea(attrs={
                'class': 'textarea textarea-bordered w-full',
                'rows': 3,
                'placeholder': 'Custom AI analysis prompt (optional)'
            }),
        }

    def __init__(self, *args, user=None, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            # Filter categories to user's own categories
            self.fields['category'].queryset = SurveyCategory.objects.filter(created_by=user)


class SurveyQuestionForm(forms.ModelForm):
    """Form for creating and editing survey questions."""
    
    class Meta:
        model = SurveyQuestion
        fields = [
            'question_text', 'question_type', 'is_required', 'help_text',
            'weight', 'scale_min', 'scale_max'
        ]
        widgets = {
            'question_text': forms.Textarea(attrs={
                'class': 'textarea textarea-bordered w-full',
                'rows': 3,
                'placeholder': 'Enter your question'
            }),
            'question_type': forms.Select(attrs={
                'class': 'select select-bordered w-full',
                'onchange': 'toggleScaleFields()'
            }),
            'help_text': forms.Textarea(attrs={
                'class': 'textarea textarea-bordered w-full',
                'rows': 2,
                'placeholder': 'Additional guidance for respondents (optional)'
            }),
            'weight': forms.NumberInput(attrs={
                'class': 'input input-bordered w-full',
                'step': '0.1',
                'min': '0'
            }),
            'scale_min': forms.NumberInput(attrs={
                'class': 'input input-bordered w-full',
                'min': '1'
            }),
            'scale_max': forms.NumberInput(attrs={
                'class': 'input input-bordered w-full',
                'min': '2'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        question_type = cleaned_data.get('question_type')
        scale_min = cleaned_data.get('scale_min')
        scale_max = cleaned_data.get('scale_max')

        if question_type == 'SCALE_CUSTOM':
            if not scale_min or not scale_max:
                raise ValidationError("Custom scale questions require both minimum and maximum values.")
            if scale_min >= scale_max:
                raise ValidationError("Scale maximum must be greater than minimum.")
            if scale_max - scale_min > 20:
                raise ValidationError("Scale range cannot exceed 20 points.")

        return cleaned_data


class SurveyQuestionChoiceForm(forms.ModelForm):
    """Form for multiple choice options."""
    
    class Meta:
        model = SurveyQuestionChoice
        fields = ['text', 'value', 'order']
        widgets = {
            'text': forms.TextInput(attrs={
                'class': 'input input-bordered w-full',
                'placeholder': 'Choice text'
            }),
            'value': forms.NumberInput(attrs={
                'class': 'input input-bordered w-full',
                'placeholder': 'Numeric value for scoring'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'input input-bordered w-full',
                'min': '0'
            }),
        }


class SurveyResponseForm(forms.Form):
    """Dynamic form for survey responses based on question type."""
    
    def __init__(self, *args, question=None, **kwargs):
        super().__init__(*args, **kwargs)
        
        if question:
            self.question = question
            field_name = f'question_{question.id}'
            
            if question.question_type == 'LIKERT_5':
                choices = [(i, str(i)) for i in range(1, 6)]
                self.fields[field_name] = forms.ChoiceField(
                    choices=choices,
                    widget=forms.RadioSelect(attrs={
                        'class': 'radio radio-primary'
                    }),
                    required=question.is_required,
                    label=question.question_text
                )
            
            elif question.question_type == 'LIKERT_7':
                choices = [(i, str(i)) for i in range(1, 8)]
                self.fields[field_name] = forms.ChoiceField(
                    choices=choices,
                    widget=forms.RadioSelect(attrs={
                        'class': 'radio radio-primary'
                    }),
                    required=question.is_required,
                    label=question.question_text
                )
            
            elif question.question_type == 'RATING_10':
                choices = [(i, str(i)) for i in range(1, 11)]
                self.fields[field_name] = forms.ChoiceField(
                    choices=choices,
                    widget=forms.RadioSelect(attrs={
                        'class': 'radio radio-primary'
                    }),
                    required=question.is_required,
                    label=question.question_text
                )
            
            elif question.question_type == 'SCALE_CUSTOM':
                if question.scale_min and question.scale_max:
                    choices = []
                    for i in range(question.scale_min, question.scale_max + 1):
                        label = question.scale_labels.get(str(i), str(i))
                        choices.append((i, label))
                    
                    self.fields[field_name] = forms.ChoiceField(
                        choices=choices,
                        widget=forms.RadioSelect(attrs={
                            'class': 'radio radio-primary'
                        }),
                        required=question.is_required,
                        label=question.question_text
                    )
            
            elif question.question_type == 'MULTIPLE_CHOICE':
                choices = [(choice.id, choice.text) for choice in question.choices.all()]
                self.fields[field_name] = forms.ChoiceField(
                    choices=choices,
                    widget=forms.RadioSelect(attrs={
                        'class': 'radio radio-primary'
                    }),
                    required=question.is_required,
                    label=question.question_text
                )
            
            elif question.question_type == 'YES_NO':
                self.fields[field_name] = forms.ChoiceField(
                    choices=[(1, 'Yes'), (0, 'No')],
                    widget=forms.RadioSelect(attrs={
                        'class': 'radio radio-primary'
                    }),
                    required=question.is_required,
                    label=question.question_text
                )
            
            elif question.question_type == 'TEXT_SHORT':
                self.fields[field_name] = forms.CharField(
                    max_length=500,
                    widget=forms.TextInput(attrs={
                        'class': 'input input-bordered w-full',
                        'placeholder': 'Enter your response'
                    }),
                    required=question.is_required,
                    label=question.question_text
                )
            
            elif question.question_type == 'TEXT_LONG':
                self.fields[field_name] = forms.CharField(
                    widget=forms.Textarea(attrs={
                        'class': 'textarea textarea-bordered w-full',
                        'rows': 4,
                        'placeholder': 'Enter your detailed response'
                    }),
                    required=question.is_required,
                    label=question.question_text
                )
            
            # Add help text if available
            if question.help_text:
                self.fields[field_name].help_text = question.help_text


class SurveyCategoryForm(forms.ModelForm):
    """Form for creating survey categories."""
    
    class Meta:
        model = SurveyCategory
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'input input-bordered w-full',
                'placeholder': 'Category name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'textarea textarea-bordered w-full',
                'rows': 3,
                'placeholder': 'Category description'
            }),
        }


# Formset for managing multiple choice options
SurveyQuestionChoiceFormSet = forms.inlineformset_factory(
    SurveyQuestion,
    SurveyQuestionChoice,
    form=SurveyQuestionChoiceForm,
    extra=3,
    can_delete=True,
    min_num=2,
    validate_min=True
)
