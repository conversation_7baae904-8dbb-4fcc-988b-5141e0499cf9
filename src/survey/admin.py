from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import (
    SurveyCategory, Survey, SurveyQuestion, SurveyQuestionChoice,
    SurveySession, SurveyResponse, SurveyAnalytics
)


@admin.register(SurveyCategory)
class SurveyCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_by', 'survey_count', 'created_at']
    list_filter = ['created_at', 'created_by']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['created_at']

    def survey_count(self, obj):
        return obj.surveys.count()
    survey_count.short_description = 'Surveys'


class SurveyQuestionChoiceInline(admin.TabularInline):
    model = SurveyQuestionChoice
    extra = 0
    fields = ['text', 'value', 'order']


class SurveyQuestionInline(admin.TabularInline):
    model = SurveyQuestion
    extra = 0
    fields = ['question_text', 'question_type', 'order', 'weight', 'is_required', 'is_active']
    readonly_fields = ['created_at']


@admin.register(Survey)
class SurveyAdmin(admin.ModelAdmin):
    list_display = ['title', 'survey_type', 'category', 'created_by', 'is_published', 'response_count', 'avg_score', 'created_at']
    list_filter = ['survey_type', 'category', 'is_published', 'enable_scoring', 'enable_ai_analysis', 'created_at']
    search_fields = ['title', 'description']
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ['created_at', 'updated_at', 'total_questions', 'total_responses', 'average_score']
    inlines = [SurveyQuestionInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'description', 'survey_type', 'category', 'created_by')
        }),
        ('Settings', {
            'fields': ('random_order', 'allow_anonymous', 'multiple_responses', 'is_published')
        }),
        ('Scoring & Analysis', {
            'fields': ('enable_scoring', 'scoring_rubric', 'enable_ai_analysis', 'ai_analysis_prompt')
        }),
        ('Statistics', {
            'fields': ('total_questions', 'total_responses', 'average_score'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def response_count(self, obj):
        count = obj.sessions.filter(is_complete=True).count()
        if count > 0:
            url = reverse('admin:survey_surveysession_changelist') + f'?survey__id__exact={obj.id}'
            return format_html('<a href="{}">{}</a>', url, count)
        return count
    response_count.short_description = 'Responses'

    def avg_score(self, obj):
        avg = obj.average_score
        if avg:
            return f"{avg:.1f}%"
        return "-"
    avg_score.short_description = 'Avg Score'


@admin.register(SurveyQuestion)
class SurveyQuestionAdmin(admin.ModelAdmin):
    list_display = ['survey', 'question_text_short', 'question_type', 'order', 'weight', 'is_required', 'is_active']
    list_filter = ['question_type', 'is_required', 'is_active', 'survey__survey_type']
    search_fields = ['question_text', 'survey__title']
    list_editable = ['order', 'weight', 'is_required', 'is_active']
    inlines = [SurveyQuestionChoiceInline]

    fieldsets = (
        ('Question Details', {
            'fields': ('survey', 'question_text', 'question_type', 'order', 'help_text')
        }),
        ('Configuration', {
            'fields': ('is_required', 'weight', 'is_active')
        }),
        ('Custom Scale Settings', {
            'fields': ('scale_min', 'scale_max', 'scale_labels'),
            'classes': ('collapse',),
            'description': 'Only used for custom scale questions'
        }),
        ('Scoring', {
            'fields': ('scoring_config',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def question_text_short(self, obj):
        return obj.question_text[:50] + "..." if len(obj.question_text) > 50 else obj.question_text
    question_text_short.short_description = 'Question'


@admin.register(SurveySession)
class SurveySessionAdmin(admin.ModelAdmin):
    list_display = ['id_short', 'survey', 'user_display', 'score_display', 'performance_level', 'is_complete', 'completed_at']
    list_filter = ['is_complete', 'survey__survey_type', 'ai_analysis_completed', 'completed_at']
    search_fields = ['user__username', 'survey__title', 'session_key']
    readonly_fields = ['id', 'started_at', 'completed_at', 'time_spent', 'progress_percentage', 'score_percentage']

    fieldsets = (
        ('Session Information', {
            'fields': ('id', 'survey', 'user', 'session_key', 'is_complete')
        }),
        ('Progress', {
            'fields': ('question_order', 'current_question_index', 'progress_percentage')
        }),
        ('Scoring', {
            'fields': ('total_score', 'max_possible_score', 'score_percentage', 'score_breakdown')
        }),
        ('AI Analysis', {
            'fields': ('ai_analysis_completed', 'ai_analysis_results'),
            'classes': ('collapse',)
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at', 'time_spent')
        }),
        ('Metadata', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
    )

    def id_short(self, obj):
        return str(obj.id)[:8] + "..."
    id_short.short_description = 'Session ID'

    def user_display(self, obj):
        if obj.user:
            return obj.user.username
        return f"Anonymous ({obj.session_key[:8]}...)"
    user_display.short_description = 'User'

    def score_display(self, obj):
        if obj.total_score is not None:
            return f"{obj.score_percentage:.1f}% ({obj.total_score}/{obj.max_possible_score})"
        return "-"
    score_display.short_description = 'Score'

    def performance_level(self, obj):
        if obj.ai_analysis_results and 'performance_level' in obj.ai_analysis_results:
            level = obj.ai_analysis_results['performance_level']
            colors = {
                'Exceptional': 'green',
                'Excellent': 'blue',
                'Good': 'orange',
                'Satisfactory': 'gray',
                'Needs Improvement': 'red',
                'Requires Significant Development': 'darkred'
            }
            color = colors.get(level, 'gray')
            return format_html('<span style="color: {};">{}</span>', color, level)
        return "-"
    performance_level.short_description = 'Performance'


@admin.register(SurveyResponse)
class SurveyResponseAdmin(admin.ModelAdmin):
    list_display = ['session_short', 'question_short', 'response_value', 'calculated_score', 'responded_at']
    list_filter = ['question__question_type', 'responded_at']
    search_fields = ['session__user__username', 'question__question_text', 'text_value']
    readonly_fields = ['responded_at', 'calculated_score']

    def session_short(self, obj):
        return str(obj.session.id)[:8] + "..."
    session_short.short_description = 'Session'

    def question_short(self, obj):
        return obj.question.question_text[:30] + "..." if len(obj.question.question_text) > 30 else obj.question.question_text
    question_short.short_description = 'Question'

    def response_value(self, obj):
        return obj.get_display_value()
    response_value.short_description = 'Response'


@admin.register(SurveyAnalytics)
class SurveyAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['survey', 'total_responses', 'completed_responses', 'average_score', 'last_updated']
    list_filter = ['last_updated']
    search_fields = ['survey__title']
    readonly_fields = ['last_updated']

    fieldsets = (
        ('Survey', {
            'fields': ('survey',)
        }),
        ('Response Statistics', {
            'fields': ('total_responses', 'completed_responses', 'anonymous_responses')
        }),
        ('Score Statistics', {
            'fields': ('average_score', 'median_score', 'highest_score', 'lowest_score', 'score_distribution')
        }),
        ('Timing Statistics', {
            'fields': ('average_completion_time', 'median_completion_time')
        }),
        ('Detailed Analytics', {
            'fields': ('question_analytics', 'ai_insights', 'ai_summary'),
            'classes': ('collapse',)
        }),
        ('Last Updated', {
            'fields': ('last_updated',)
        }),
    )
