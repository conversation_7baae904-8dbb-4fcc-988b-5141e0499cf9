from django.urls import path
from . import views

app_name = 'survey'

urlpatterns = [
    # Survey list and management
    path('', views.SurveyListView.as_view(), name='survey_list'),
    path('create/', views.SurveyCreateView.as_view(), name='survey_create'),
    path('<slug:slug>/', views.SurveyDetailView.as_view(), name='survey_detail'),
    path('<slug:slug>/edit/', views.SurveyEditView.as_view(), name='survey_edit'),
    path('<slug:slug>/delete/', views.SurveyDeleteView.as_view(), name='survey_delete'),
    
    # Survey taking
    path('<slug:slug>/start/', views.start_survey, name='start_survey'),
    path('session/<uuid:session_id>/', views.take_survey, name='take_survey'),
    path('session/<uuid:session_id>/results/', views.survey_results, name='survey_results'),
    
    # Survey analytics
    path('<slug:slug>/analytics/', views.SurveyAnalyticsView.as_view(), name='survey_analytics'),
    path('<slug:slug>/responses/', views.SurveyResponsesView.as_view(), name='survey_responses'),
    
    # HTMX endpoints
    path('htmx/question/<int:question_id>/', views.htmx_question_detail, name='htmx_question_detail'),
    path('htmx/submit-answer/', views.htmx_submit_answer, name='htmx_submit_answer'),
]
