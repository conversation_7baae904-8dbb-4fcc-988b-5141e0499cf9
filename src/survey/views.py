from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.http import JsonResponse, HttpResponse
from django.urls import reverse, reverse_lazy
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.core.paginator import Paginator
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json

from .models import (
    Survey, SurveyQuestion, SurveySession, SurveyResponse,
    SurveyCategory, SurveyAnalytics, SurveyQuestionChoice
)
from .forms import SurveyForm, SurveyQuestionForm, SurveyCategoryForm, SurveyResponseForm
from .utils import SurveySessionManager, SurveyResponse<PERSON>and<PERSON>, SurveyQuestionRenderer
from logger.utils import survey_logger, log_survey_event, log_user_action


class SurveyListView(ListView):
    """List all available surveys."""
    model = Survey
    template_name = 'survey/survey_list.html'
    context_object_name = 'surveys'
    paginate_by = 12

    def get_queryset(self):
        queryset = Survey.objects.filter(is_published=True)

        # Filter by category if specified
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__slug=category)

        # Filter by survey type if specified
        survey_type = self.request.GET.get('type')
        if survey_type:
            queryset = queryset.filter(survey_type=survey_type)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset.select_related('category', 'created_by').order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = SurveyCategory.objects.all()
        context['survey_types'] = Survey.SURVEY_TYPES
        context['current_category'] = self.request.GET.get('category', '')
        context['current_type'] = self.request.GET.get('type', '')
        context['search_query'] = self.request.GET.get('search', '')
        return context


class SurveyDetailView(DetailView):
    """Display survey details and start button."""
    model = Survey
    template_name = 'survey/survey_detail.html'
    context_object_name = 'survey'

    def get_queryset(self):
        return Survey.objects.filter(is_published=True).select_related('category', 'created_by')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        survey = self.object

        # Check if user has already taken this survey
        user_session = None
        if self.request.user.is_authenticated:
            user_session = SurveySession.objects.filter(
                survey=survey,
                user=self.request.user,
                is_complete=True
            ).first()

        context['user_has_completed'] = bool(user_session)
        context['user_session'] = user_session
        context['can_retake'] = survey.multiple_responses or not user_session
        context['total_questions'] = survey.total_questions
        context['estimated_time'] = survey.total_questions * 1.5  # Estimate 1.5 minutes per question

        return context


def start_survey(request, slug):
    """Start a new survey session."""
    survey = get_object_or_404(Survey, slug=slug, is_published=True)

    # Check authentication requirements
    if not survey.allow_anonymous and not request.user.is_authenticated:
        messages.error(request, "You must be logged in to take this survey.")
        return redirect('account_login')

    try:
        # Create session manager
        session_manager = SurveySessionManager(
            survey=survey,
            user=request.user if request.user.is_authenticated else None,
            session_key=request.session.session_key,
            request=request
        )

        # Create new session
        session = session_manager.create_session()

        messages.success(request, f"Survey '{survey.title}' started! Good luck!")
        return redirect('survey:take_survey', session_id=session.id)

    except ValueError as e:
        messages.error(request, str(e))
        return redirect('survey:survey_detail', slug=slug)


def take_survey(request, session_id):
    """Main survey taking view with HTMX support."""
    import logging
    logger = logging.getLogger('survey.views')

    logger.info(f"=== TAKE_SURVEY DEBUG START ===")
    logger.info(f"Session ID: {session_id}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"User: {request.user}")

    session = get_object_or_404(SurveySession, id=session_id, is_complete=False)
    logger.info(f"Session found: {session}")

    # Verify session ownership
    if session.user and session.user != request.user:
        messages.error(request, "You don't have permission to access this survey session.")
        return redirect('survey:survey_list')

    current_question = session.current_question
    logger.info(f"Current question: {current_question}")
    logger.info(f"Question type: {current_question.question_type if current_question else 'None'}")

    if not current_question:
        # Survey is complete
        return redirect('survey:survey_results', session_id=session.id)

    # Handle answer submission
    if request.method == 'POST':
        logger.info(f"POST data: {request.POST}")
        return handle_answer_submission(request, session, current_question)

    # Get existing answer if any
    existing_answer = SurveyResponse.objects.filter(
        session=session,
        question=current_question
    ).first()
    logger.info(f"Existing answer: {existing_answer}")

    # Get question context for rendering
    question_context = SurveyQuestionRenderer.get_question_context(
        current_question, existing_answer
    )
    logger.info(f"Question context keys: {list(question_context.keys())}")

    if 'scale_options' in question_context:
        logger.info(f"Scale options count: {len(question_context['scale_options'])}")
        for i, option in enumerate(question_context['scale_options']):
            logger.info(f"  Option {i}: {option}")

    context = {
        'session': session,
        'survey': session.survey,
        'question': current_question,
        'existing_answer': existing_answer,
        'progress': {
            'current': session.current_question_index + 1,
            'total': len(session.question_order),
            'percentage': session.progress_percentage
        },
        **question_context
    }

    logger.info(f"Final context keys: {list(context.keys())}")
    logger.info(f"=== TAKE_SURVEY DEBUG END ===")

    return render(request, 'survey/take_survey.html', context)


def handle_answer_submission(request, session, question):
    """Handle answer submission with HTMX."""
    start_time = timezone.now()

    try:
        # Create response handler
        response_handler = SurveyResponseHandler(session)

        # Prepare response data based on question type
        response_data = {}

        if question.question_type in ['LIKERT_5', 'LIKERT_7', 'RATING_10', 'SCALE_CUSTOM', 'YES_NO']:
            response_data['numeric_value'] = request.POST.get(f'question_{question.id}')

        elif question.question_type == 'MULTIPLE_CHOICE':
            response_data['choice_id'] = request.POST.get(f'question_{question.id}')

        elif question.question_type in ['TEXT_SHORT', 'TEXT_LONG']:
            response_data['text_value'] = request.POST.get(f'question_{question.id}', '').strip()

        # Save response
        response_handler.save_response(question, response_data)

        # Advance to next question
        is_complete = response_handler.advance_session()

        if is_complete:
            # Survey completed
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    headers={'HX-Redirect': reverse('survey:survey_results', kwargs={'session_id': session.id})}
                )
            return redirect('survey:survey_results', session_id=session.id)

        # Continue to next question
        if request.headers.get('HX-Request'):
            next_question = session.current_question
            question_context = SurveyQuestionRenderer.get_question_context(next_question)

            context = {
                'session': session,
                'survey': session.survey,
                'question': next_question,
                'existing_answer': None,
                'progress': {
                    'current': session.current_question_index + 1,
                    'total': len(session.question_order),
                    'percentage': session.progress_percentage
                },
                **question_context
            }
            return render(request, 'survey/partials/question_content.html', context)

        return redirect('survey:take_survey', session_id=session.id)

    except Exception as e:
        survey_logger.error(f"Error submitting answer: {str(e)}", user=request.user, extra_data={
            'session_id': str(session.id),
            'question_id': question.id,
            'error': str(e)
        })

        if request.headers.get('HX-Request'):
            return HttpResponse(
                '<div class="alert alert-error">An error occurred. Please try again.</div>',
                status=400
            )

        messages.error(request, "An error occurred while saving your response. Please try again.")
        return redirect('survey:take_survey', session_id=session.id)


def survey_results(request, session_id):
    """Display survey results and analysis."""
    session = get_object_or_404(SurveySession, id=session_id, is_complete=True)

    # Verify session ownership
    if session.user and session.user != request.user:
        messages.error(request, "You don't have permission to view these results.")
        return redirect('survey:survey_list')

    # Get all responses
    responses = session.responses.all().select_related('question', 'choice_value')

    # Organize responses by question
    response_data = {}
    for response in responses:
        response_data[response.question.id] = {
            'question': response.question,
            'response': response,
            'display_value': response.get_display_value(),
            'score': response.calculated_score
        }

    context = {
        'session': session,
        'survey': session.survey,
        'responses': response_data,
        'total_score': session.total_score,
        'max_score': session.max_possible_score,
        'score_percentage': session.score_percentage,
        'score_breakdown': session.score_breakdown,
        'ai_analysis': session.ai_analysis_results,
        'completion_time': session.time_spent
    }

    return render(request, 'survey/survey_results.html', context)


# HTMX Views
def htmx_question_detail(request, question_id):
    """HTMX endpoint for question details."""
    question = get_object_or_404(SurveyQuestion, id=question_id)
    context = SurveyQuestionRenderer.get_question_context(question)
    return render(request, 'survey/partials/question_detail.html', context)


@csrf_exempt
def htmx_submit_answer(request):
    """HTMX endpoint for submitting answers."""
    if request.method != 'POST':
        return HttpResponse(status=405)

    session_id = request.POST.get('session_id')
    question_id = request.POST.get('question_id')

    if not session_id or not question_id:
        return HttpResponse('Missing required parameters', status=400)

    try:
        session = SurveySession.objects.get(id=session_id, is_complete=False)
        question = SurveyQuestion.objects.get(id=question_id)

        return handle_answer_submission(request, session, question)

    except (SurveySession.DoesNotExist, SurveyQuestion.DoesNotExist):
        return HttpResponse('Invalid session or question', status=404)


def test_radio(request):
    """Test radio button functionality."""
    return render(request, 'survey/test_radio.html')


# Management Views (for survey creators)
class SurveyCreateView(LoginRequiredMixin, CreateView):
    """Create a new survey."""
    model = Survey
    form_class = SurveyForm
    template_name = 'survey/survey_create.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        response = super().form_valid(form)

        log_user_action('survey_created', self.request.user, 'survey', self.object.id, {
            'survey_title': self.object.title,
            'survey_type': self.object.survey_type
        })

        messages.success(self.request, f"Survey '{self.object.title}' created successfully!")
        return response

    def get_success_url(self):
        return reverse('survey:survey_detail', kwargs={'slug': self.object.slug})


class SurveyEditView(LoginRequiredMixin, UpdateView):
    """Edit an existing survey."""
    model = Survey
    form_class = SurveyForm
    template_name = 'survey/survey_edit.html'

    def get_queryset(self):
        return Survey.objects.filter(created_by=self.request.user)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)

        log_user_action('survey_updated', self.request.user, 'survey', self.object.id, {
            'survey_title': self.object.title
        })

        messages.success(self.request, f"Survey '{self.object.title}' updated successfully!")
        return response

    def get_success_url(self):
        return reverse('survey:survey_detail', kwargs={'slug': self.object.slug})


class SurveyDeleteView(LoginRequiredMixin, DeleteView):
    """Delete a survey."""
    model = Survey
    template_name = 'survey/survey_delete.html'
    success_url = reverse_lazy('survey:survey_list')

    def get_queryset(self):
        return Survey.objects.filter(created_by=self.request.user)

    def delete(self, request, *args, **kwargs):
        survey = self.get_object()
        survey_title = survey.title

        log_user_action('survey_deleted', request.user, 'survey', survey.id, {
            'survey_title': survey_title
        })

        messages.success(request, f"Survey '{survey_title}' deleted successfully!")
        return super().delete(request, *args, **kwargs)


class SurveyAnalyticsView(LoginRequiredMixin, DetailView):
    """Display survey analytics and insights."""
    model = Survey
    template_name = 'survey/survey_analytics.html'
    context_object_name = 'survey'

    def get_queryset(self):
        return Survey.objects.filter(created_by=self.request.user).select_related('analytics')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        survey = self.object

        # Update analytics if needed
        if hasattr(survey, 'analytics'):
            survey.analytics.update_analytics()
            context['analytics'] = survey.analytics

        # Get recent responses
        recent_responses = SurveySession.objects.filter(
            survey=survey,
            is_complete=True
        ).order_by('-completed_at')[:10]

        context['recent_responses'] = recent_responses
        context['total_responses'] = survey.sessions.filter(is_complete=True).count()

        return context


class SurveyResponsesView(LoginRequiredMixin, ListView):
    """List all responses for a survey."""
    model = SurveySession
    template_name = 'survey/survey_responses.html'
    context_object_name = 'sessions'
    paginate_by = 20

    def get_queryset(self):
        self.survey = get_object_or_404(
            Survey,
            slug=self.kwargs['slug'],
            created_by=self.request.user
        )
        return SurveySession.objects.filter(
            survey=self.survey,
            is_complete=True
        ).select_related('user').order_by('-completed_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['survey'] = self.survey
        return context
