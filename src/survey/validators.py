from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from .models import SurveyQuestion, SurveyQuestionChoice


class SurveyResponseValidator:
    """Validator for survey responses based on question type."""
    
    def __init__(self, question):
        self.question = question
    
    def validate_response(self, value):
        """Validate a response value based on question type."""
        if self.question.is_required and (value is None or value == ''):
            raise ValidationError(_('This question is required.'))
        
        if value is None or value == '':
            return  # Optional question with no response is valid
        
        # Validate based on question type
        if self.question.question_type == 'LIKERT_5':
            self._validate_likert_5(value)
        elif self.question.question_type == 'LIKERT_7':
            self._validate_likert_7(value)
        elif self.question.question_type == 'RATING_10':
            self._validate_rating_10(value)
        elif self.question.question_type == 'SCALE_CUSTOM':
            self._validate_custom_scale(value)
        elif self.question.question_type == 'MULTIPLE_CHOICE':
            self._validate_multiple_choice(value)
        elif self.question.question_type == 'YES_NO':
            self._validate_yes_no(value)
        elif self.question.question_type == 'TEXT_SHORT':
            self._validate_text_short(value)
        elif self.question.question_type == 'TEXT_LONG':
            self._validate_text_long(value)
    
    def _validate_likert_5(self, value):
        """Validate Likert 5-point scale response."""
        try:
            numeric_value = int(value)
            if not (1 <= numeric_value <= 5):
                raise ValidationError(_('Value must be between 1 and 5.'))
        except (ValueError, TypeError):
            raise ValidationError(_('Invalid response format. Expected a number between 1 and 5.'))
    
    def _validate_likert_7(self, value):
        """Validate Likert 7-point scale response."""
        try:
            numeric_value = int(value)
            if not (1 <= numeric_value <= 7):
                raise ValidationError(_('Value must be between 1 and 7.'))
        except (ValueError, TypeError):
            raise ValidationError(_('Invalid response format. Expected a number between 1 and 7.'))
    
    def _validate_rating_10(self, value):
        """Validate 10-point rating scale response."""
        try:
            numeric_value = int(value)
            if not (1 <= numeric_value <= 10):
                raise ValidationError(_('Value must be between 1 and 10.'))
        except (ValueError, TypeError):
            raise ValidationError(_('Invalid response format. Expected a number between 1 and 10.'))
    
    def _validate_custom_scale(self, value):
        """Validate custom scale response."""
        if not self.question.scale_min or not self.question.scale_max:
            raise ValidationError(_('Custom scale not properly configured.'))
        
        try:
            numeric_value = int(value)
            if not (self.question.scale_min <= numeric_value <= self.question.scale_max):
                raise ValidationError(
                    _('Value must be between %(min)s and %(max)s.') % {
                        'min': self.question.scale_min,
                        'max': self.question.scale_max
                    }
                )
        except (ValueError, TypeError):
            raise ValidationError(
                _('Invalid response format. Expected a number between %(min)s and %(max)s.') % {
                    'min': self.question.scale_min,
                    'max': self.question.scale_max
                }
            )
    
    def _validate_multiple_choice(self, value):
        """Validate multiple choice response."""
        try:
            choice_id = int(value)
            if not SurveyQuestionChoice.objects.filter(
                question=self.question,
                id=choice_id
            ).exists():
                raise ValidationError(_('Invalid choice selected.'))
        except (ValueError, TypeError):
            raise ValidationError(_('Invalid choice format.'))
    
    def _validate_yes_no(self, value):
        """Validate Yes/No response."""
        try:
            numeric_value = int(value)
            if numeric_value not in [0, 1]:
                raise ValidationError(_('Value must be 0 (No) or 1 (Yes).'))
        except (ValueError, TypeError):
            raise ValidationError(_('Invalid response format. Expected 0 or 1.'))
    
    def _validate_text_short(self, value):
        """Validate short text response."""
        if not isinstance(value, str):
            raise ValidationError(_('Text response expected.'))
        
        if len(value.strip()) == 0:
            if self.question.is_required:
                raise ValidationError(_('This question requires a text response.'))
            return
        
        if len(value) > 500:
            raise ValidationError(_('Response too long. Maximum 500 characters allowed.'))
        
        # Check for minimum meaningful content
        if len(value.strip()) < 2:
            raise ValidationError(_('Please provide a more detailed response.'))
    
    def _validate_text_long(self, value):
        """Validate long text response."""
        if not isinstance(value, str):
            raise ValidationError(_('Text response expected.'))
        
        if len(value.strip()) == 0:
            if self.question.is_required:
                raise ValidationError(_('This question requires a text response.'))
            return
        
        if len(value) > 5000:
            raise ValidationError(_('Response too long. Maximum 5000 characters allowed.'))
        
        # Check for minimum meaningful content
        if len(value.strip()) < 10:
            raise ValidationError(_('Please provide a more detailed response (minimum 10 characters).'))


def validate_survey_completion(survey, responses):
    """Validate that all required questions have been answered."""
    required_questions = survey.questions.filter(is_required=True, is_active=True)
    answered_question_ids = set(response.question.id for response in responses)
    
    missing_questions = []
    for question in required_questions:
        if question.id not in answered_question_ids:
            missing_questions.append(question.question_text[:50] + "...")
    
    if missing_questions:
        raise ValidationError(
            _('The following required questions must be answered: %(questions)s') % {
                'questions': ', '.join(missing_questions)
            }
        )


def validate_survey_question_order(survey):
    """Validate that question orders are unique and sequential."""
    questions = survey.questions.filter(is_active=True).order_by('order')
    orders = [q.order for q in questions]
    
    # Check for duplicates
    if len(orders) != len(set(orders)):
        raise ValidationError(_('Question orders must be unique.'))
    
    # Check for gaps (optional - could be allowed)
    if orders and orders != list(range(min(orders), max(orders) + 1)):
        # This is a warning, not an error - gaps are allowed
        pass


def validate_survey_scoring_config(survey):
    """Validate survey scoring configuration."""
    if not survey.enable_scoring:
        return
    
    for question in survey.questions.filter(is_active=True):
        if question.question_type == 'SCALE_CUSTOM':
            if not question.scale_min or not question.scale_max:
                raise ValidationError(
                    _('Custom scale question "%(question)s" must have min and max values.') % {
                        'question': question.question_text[:50]
                    }
                )
        
        elif question.question_type == 'MULTIPLE_CHOICE':
            if not question.choices.exists():
                raise ValidationError(
                    _('Multiple choice question "%(question)s" must have at least one choice.') % {
                        'question': question.question_text[:50]
                    }
                )


class SurveyQuestionValidator:
    """Validator for survey question configuration."""
    
    @staticmethod
    def validate_question_type_config(question):
        """Validate question configuration based on type."""
        if question.question_type == 'SCALE_CUSTOM':
            if not question.scale_min or not question.scale_max:
                raise ValidationError(_('Custom scale questions require both min and max values.'))
            
            if question.scale_min >= question.scale_max:
                raise ValidationError(_('Scale maximum must be greater than minimum.'))
            
            if question.scale_max - question.scale_min > 20:
                raise ValidationError(_('Scale range cannot exceed 20 points.'))
        
        elif question.question_type == 'MULTIPLE_CHOICE':
            # This will be validated after choices are added
            pass
        
        # Weight validation
        if question.weight < 0:
            raise ValidationError(_('Question weight cannot be negative.'))
        
        if question.weight > 10:
            raise ValidationError(_('Question weight cannot exceed 10.'))


def validate_choice_values(question):
    """Validate multiple choice values for scoring."""
    if question.question_type != 'MULTIPLE_CHOICE':
        return
    
    choices = question.choices.all()
    if not choices:
        raise ValidationError(_('Multiple choice questions must have at least one choice.'))
    
    if len(choices) < 2:
        raise ValidationError(_('Multiple choice questions must have at least two choices.'))
    
    # Check for duplicate values
    values = [choice.value for choice in choices]
    if len(values) != len(set(values)):
        raise ValidationError(_('Choice values must be unique.'))
    
    # Check for duplicate orders
    orders = [choice.order for choice in choices]
    if len(orders) != len(set(orders)):
        raise ValidationError(_('Choice orders must be unique.'))
