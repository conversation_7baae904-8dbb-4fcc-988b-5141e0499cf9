from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify
import json
import uuid


class SurveyCategory(models.Model):
    """Survey categories for organizing surveys by type."""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    slug = models.SlugField(unique=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_survey_categories')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Survey Categories"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} (by {self.created_by.username})"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


class Survey(models.Model):
    """Main survey model containing questions and settings."""

    SURVEY_TYPES = [
        ('FACE_TO_FACE', 'Face-to-Face Benchmark'),
        ('PHONE', 'Phone Benchmark'),
        ('ZOOM', 'Zoom Benchmark'),
        ('CUSTOM', 'Custom Survey'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField()
    survey_type = models.CharField(max_length=20, choices=SURVEY_TYPES, default='CUSTOM')
    category = models.ForeignKey(SurveyCategory, on_delete=models.CASCADE, related_name='surveys')
    slug = models.SlugField()
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_surveys')

    # Survey settings
    random_order = models.BooleanField(default=False, help_text="Randomize question order")
    allow_anonymous = models.BooleanField(default=False, help_text="Allow anonymous responses")
    multiple_responses = models.BooleanField(default=False, help_text="Allow multiple responses per user")

    # Scoring settings
    enable_scoring = models.BooleanField(default=True, help_text="Enable automatic scoring")
    scoring_rubric = models.JSONField(default=dict, blank=True, help_text="Scoring rules and thresholds")

    # AI Analysis settings
    enable_ai_analysis = models.BooleanField(default=True, help_text="Enable AI-powered analysis")
    ai_analysis_prompt = models.TextField(
        blank=True,
        help_text="Custom prompt for AI analysis (leave blank for default)"
    )

    # Status
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        unique_together = ['slug', 'created_by']

    def __str__(self):
        return f"{self.title} ({self.get_survey_type_display()})"

    def get_absolute_url(self):
        return reverse('survey:survey_detail', kwargs={'slug': self.slug})

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.title)
            slug = base_slug
            counter = 1
            while Survey.objects.filter(slug=slug, created_by=self.created_by).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug
        super().save(*args, **kwargs)

    @property
    def total_questions(self):
        return self.questions.count()

    @property
    def total_responses(self):
        return self.sessions.filter(is_complete=True).count()

    @property
    def average_score(self):
        completed_sessions = self.sessions.filter(is_complete=True, total_score__isnull=False)
        if completed_sessions.exists():
            return completed_sessions.aggregate(avg_score=models.Avg('total_score'))['avg_score']
        return 0


class SurveyQuestion(models.Model):
    """Survey questions with various response types."""

    QUESTION_TYPES = [
        ('LIKERT_5', 'Likert Scale (1-5)'),
        ('LIKERT_7', 'Likert Scale (1-7)'),
        ('RATING_10', 'Rating Scale (1-10)'),
        ('MULTIPLE_CHOICE', 'Multiple Choice'),
        ('YES_NO', 'Yes/No'),
        ('TEXT_SHORT', 'Short Text Response'),
        ('TEXT_LONG', 'Long Text Response'),
        ('SCALE_CUSTOM', 'Custom Scale'),
    ]

    survey = models.ForeignKey(Survey, on_delete=models.CASCADE, related_name='questions')
    question_text = models.TextField(help_text="The question text")
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES)
    order = models.PositiveIntegerField(default=0)

    # Question configuration
    is_required = models.BooleanField(default=True)
    help_text = models.TextField(blank=True, help_text="Additional guidance for respondents")

    # Scoring configuration
    weight = models.FloatField(default=1.0, help_text="Weight for scoring calculations")
    scoring_config = models.JSONField(default=dict, blank=True, help_text="Question-specific scoring rules")

    # Custom scale configuration (for SCALE_CUSTOM type)
    scale_min = models.IntegerField(null=True, blank=True, help_text="Minimum scale value")
    scale_max = models.IntegerField(null=True, blank=True, help_text="Maximum scale value")
    scale_labels = models.JSONField(default=dict, blank=True, help_text="Labels for scale points")

    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'created_at']
        unique_together = ['survey', 'order']

    def __str__(self):
        return f"{self.survey.title} - Q{self.order}: {self.question_text[:50]}..."

    def get_response_choices(self):
        """Get available response choices based on question type."""
        if self.question_type == 'LIKERT_5':
            return [(i, str(i)) for i in range(1, 6)]
        elif self.question_type == 'LIKERT_7':
            return [(i, str(i)) for i in range(1, 8)]
        elif self.question_type == 'RATING_10':
            return [(i, str(i)) for i in range(1, 11)]
        elif self.question_type == 'YES_NO':
            return [(1, 'Yes'), (0, 'No')]
        elif self.question_type == 'SCALE_CUSTOM' and self.scale_min and self.scale_max:
            choices = []
            for i in range(self.scale_min, self.scale_max + 1):
                label = self.scale_labels.get(str(i), str(i))
                choices.append((i, label))
            return choices
        elif self.question_type == 'MULTIPLE_CHOICE':
            return [(choice.id, choice.text) for choice in self.choices.all()]
        return []


class SurveyQuestionChoice(models.Model):
    """Multiple choice options for survey questions."""
    question = models.ForeignKey(
        SurveyQuestion,
        on_delete=models.CASCADE,
        related_name='choices',
        limit_choices_to={'question_type': 'MULTIPLE_CHOICE'}
    )
    text = models.CharField(max_length=500)
    value = models.IntegerField(help_text="Numeric value for scoring")
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order', 'id']
        unique_together = ['question', 'order']

    def __str__(self):
        return f"{self.question.question_text[:30]}... - {self.text}"


class SurveySessionManager(models.Manager):
    """Custom manager for SurveySession model."""

    def active(self):
        return self.filter(is_complete=False)

    def completed(self):
        return self.filter(is_complete=True)

    def for_user(self, user):
        return self.filter(user=user)

    def anonymous(self):
        return self.filter(user__isnull=True)


class SurveySession(models.Model):
    """Tracks a user's progress through a survey."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='survey_sessions', null=True, blank=True)
    survey = models.ForeignKey(Survey, on_delete=models.CASCADE, related_name='sessions')

    # Session identification for anonymous users
    session_key = models.CharField(max_length=100, blank=True, help_text="Session key for anonymous users")

    # Session data
    question_order = models.JSONField(default=list, help_text="List of question IDs in order")
    current_question_index = models.PositiveIntegerField(default=0)

    # Scoring
    total_score = models.FloatField(null=True, blank=True)
    max_possible_score = models.FloatField(null=True, blank=True)
    score_breakdown = models.JSONField(default=dict, blank=True, help_text="Detailed scoring by category")

    # AI Analysis results
    ai_analysis_results = models.JSONField(default=dict, blank=True, help_text="AI-generated insights")
    ai_analysis_completed = models.BooleanField(default=False)

    # Timing
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    time_spent = models.DurationField(null=True, blank=True)

    # Status
    is_complete = models.BooleanField(default=False)

    # Metadata
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    objects = SurveySessionManager()

    class Meta:
        ordering = ['-started_at']

    def __str__(self):
        user_display = self.user.username if self.user else f"Anonymous ({self.session_key[:8]})"
        return f"{user_display} - {self.survey.title} ({self.get_status()})"

    def get_status(self):
        if self.is_complete:
            return "Completed"
        return "In Progress"

    @property
    def current_question(self):
        if self.current_question_index < len(self.question_order):
            question_id = self.question_order[self.current_question_index]
            return SurveyQuestion.objects.get(id=question_id)
        return None

    @property
    def progress_percentage(self):
        if not self.question_order:
            return 0
        return (self.current_question_index / len(self.question_order)) * 100

    @property
    def score_percentage(self):
        if self.max_possible_score and self.total_score is not None:
            return (self.total_score / self.max_possible_score) * 100
        return 0

    def complete_session(self):
        """Mark session as complete and calculate final results."""
        self.is_complete = True
        self.completed_at = timezone.now()
        self.time_spent = self.completed_at - self.started_at
        self.calculate_scores()
        self.save()

    def calculate_scores(self):
        """Calculate total score and breakdown."""
        if not self.survey.enable_scoring:
            return

        total_score = 0
        max_score = 0
        breakdown = {}

        for response in self.responses.all():
            question = response.question
            score = response.calculate_score()
            total_score += score * question.weight
            max_score += response.get_max_score() * question.weight

            # Add to breakdown by question type or category
            question_type = question.get_question_type_display()
            if question_type not in breakdown:
                breakdown[question_type] = {'score': 0, 'max_score': 0, 'count': 0}

            breakdown[question_type]['score'] += score * question.weight
            breakdown[question_type]['max_score'] += response.get_max_score() * question.weight
            breakdown[question_type]['count'] += 1

        self.total_score = total_score
        self.max_possible_score = max_score
        self.score_breakdown = breakdown

    def get_absolute_url(self):
        return reverse('survey:survey_results', kwargs={'session_id': self.id})


class SurveyResponse(models.Model):
    """Individual responses to survey questions."""
    session = models.ForeignKey(SurveySession, on_delete=models.CASCADE, related_name='responses')
    question = models.ForeignKey(SurveyQuestion, on_delete=models.CASCADE)

    # Response data
    numeric_value = models.FloatField(null=True, blank=True, help_text="Numeric response (scales, ratings)")
    text_value = models.TextField(blank=True, help_text="Text response")
    choice_value = models.ForeignKey(
        SurveyQuestionChoice,
        on_delete=models.CASCADE,
        null=True, blank=True,
        help_text="Selected choice for multiple choice questions"
    )

    # Scoring
    calculated_score = models.FloatField(null=True, blank=True)

    # Timing
    responded_at = models.DateTimeField(auto_now_add=True)
    time_taken = models.DurationField(null=True, blank=True)

    class Meta:
        unique_together = ['session', 'question']
        ordering = ['responded_at']

    def __str__(self):
        user_display = self.session.user.username if self.session.user else "Anonymous"
        return f"{user_display} - {self.question.question_text[:30]}..."

    def get_display_value(self):
        """Get human-readable response value."""
        if self.choice_value:
            return self.choice_value.text
        elif self.numeric_value is not None:
            return str(self.numeric_value)
        elif self.text_value:
            return self.text_value
        return "No response"

    def calculate_score(self):
        """Calculate score for this response based on question type and scoring config."""
        if not self.question.survey.enable_scoring:
            return 0

        question = self.question
        scoring_config = question.scoring_config

        if question.question_type in ['LIKERT_5', 'LIKERT_7', 'RATING_10', 'SCALE_CUSTOM']:
            if self.numeric_value is not None:
                # Apply scoring rules from config or use raw value
                if 'scale_scoring' in scoring_config:
                    scale_rules = scoring_config['scale_scoring']
                    for rule in scale_rules:
                        if rule['min'] <= self.numeric_value <= rule['max']:
                            score = rule['score']
                            break
                    else:
                        score = self.numeric_value  # Default to raw value
                else:
                    score = self.numeric_value

                self.calculated_score = score
                return score

        elif question.question_type == 'MULTIPLE_CHOICE' and self.choice_value:
            score = self.choice_value.value
            self.calculated_score = score
            return score

        elif question.question_type == 'YES_NO':
            if self.numeric_value is not None:
                # 1 for Yes, 0 for No, can be weighted in scoring config
                score = self.numeric_value
                if 'yes_no_scoring' in scoring_config:
                    score = scoring_config['yes_no_scoring'].get(str(int(self.numeric_value)), score)
                self.calculated_score = score
                return score

        # Text responses don't have automatic scoring
        self.calculated_score = 0
        return 0

    def get_max_score(self):
        """Get maximum possible score for this question type."""
        question = self.question

        if question.question_type == 'LIKERT_5':
            return 5
        elif question.question_type == 'LIKERT_7':
            return 7
        elif question.question_type == 'RATING_10':
            return 10
        elif question.question_type == 'SCALE_CUSTOM' and question.scale_max:
            return question.scale_max
        elif question.question_type == 'MULTIPLE_CHOICE':
            max_choice = question.choices.aggregate(max_value=models.Max('value'))['max_value']
            return max_choice or 0
        elif question.question_type == 'YES_NO':
            return 1

        return 0


class SurveyAnalytics(models.Model):
    """Analytics and insights for survey performance."""
    survey = models.OneToOneField(Survey, on_delete=models.CASCADE, related_name='analytics')

    # Response statistics
    total_responses = models.PositiveIntegerField(default=0)
    completed_responses = models.PositiveIntegerField(default=0)
    anonymous_responses = models.PositiveIntegerField(default=0)

    # Score statistics
    average_score = models.FloatField(default=0.0)
    median_score = models.FloatField(default=0.0)
    highest_score = models.FloatField(default=0.0)
    lowest_score = models.FloatField(default=0.0)
    score_distribution = models.JSONField(default=dict, blank=True)

    # Timing statistics
    average_completion_time = models.DurationField(null=True, blank=True)
    median_completion_time = models.DurationField(null=True, blank=True)

    # Question-level analytics
    question_analytics = models.JSONField(default=dict, blank=True)

    # AI insights
    ai_insights = models.JSONField(default=dict, blank=True)
    ai_summary = models.TextField(blank=True)

    # Update tracking
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Survey Analytics"

    def __str__(self):
        return f"Analytics for {self.survey.title}"

    def update_analytics(self):
        """Recalculate all analytics for the survey."""
        completed_sessions = self.survey.sessions.filter(is_complete=True)

        # Basic statistics
        self.total_responses = self.survey.sessions.count()
        self.completed_responses = completed_sessions.count()
        self.anonymous_responses = completed_sessions.filter(user__isnull=True).count()

        if completed_sessions.exists():
            # Score statistics
            scores = [s.total_score for s in completed_sessions if s.total_score is not None]
            if scores:
                self.average_score = sum(scores) / len(scores)
                sorted_scores = sorted(scores)
                n = len(sorted_scores)
                self.median_score = sorted_scores[n//2] if n % 2 == 1 else (sorted_scores[n//2-1] + sorted_scores[n//2]) / 2
                self.highest_score = max(scores)
                self.lowest_score = min(scores)

                # Score distribution (by ranges)
                self.score_distribution = self._calculate_score_distribution(scores)

            # Timing statistics
            completion_times = [s.time_spent for s in completed_sessions if s.time_spent]
            if completion_times:
                total_seconds = sum(t.total_seconds() for t in completion_times)
                self.average_completion_time = timezone.timedelta(seconds=total_seconds / len(completion_times))

                sorted_times = sorted(completion_times)
                n = len(sorted_times)
                if n % 2 == 1:
                    self.median_completion_time = sorted_times[n//2]
                else:
                    median_seconds = (sorted_times[n//2-1].total_seconds() + sorted_times[n//2].total_seconds()) / 2
                    self.median_completion_time = timezone.timedelta(seconds=median_seconds)

            # Question-level analytics
            self.question_analytics = self._calculate_question_analytics(completed_sessions)

        self.save()

    def _calculate_score_distribution(self, scores):
        """Calculate score distribution by ranges."""
        if not scores:
            return {}

        max_score = max(scores)
        ranges = {
            'excellent': {'min': max_score * 0.8, 'max': max_score, 'count': 0},
            'good': {'min': max_score * 0.6, 'max': max_score * 0.8, 'count': 0},
            'average': {'min': max_score * 0.4, 'max': max_score * 0.6, 'count': 0},
            'below_average': {'min': max_score * 0.2, 'max': max_score * 0.4, 'count': 0},
            'poor': {'min': 0, 'max': max_score * 0.2, 'count': 0},
        }

        for score in scores:
            for range_name, range_data in ranges.items():
                if range_data['min'] <= score <= range_data['max']:
                    range_data['count'] += 1
                    break

        return ranges

    def _calculate_question_analytics(self, completed_sessions):
        """Calculate analytics for each question."""
        analytics = {}

        for question in self.survey.questions.all():
            responses = SurveyResponse.objects.filter(
                session__in=completed_sessions,
                question=question
            )

            question_data = {
                'total_responses': responses.count(),
                'response_rate': (responses.count() / completed_sessions.count() * 100) if completed_sessions.count() > 0 else 0,
            }

            if question.question_type in ['LIKERT_5', 'LIKERT_7', 'RATING_10', 'SCALE_CUSTOM']:
                numeric_responses = responses.filter(numeric_value__isnull=False)
                if numeric_responses.exists():
                    values = [r.numeric_value for r in numeric_responses]
                    question_data.update({
                        'average': sum(values) / len(values),
                        'min': min(values),
                        'max': max(values),
                        'distribution': self._calculate_value_distribution(values, question),
                    })

            elif question.question_type == 'MULTIPLE_CHOICE':
                choice_counts = {}
                for response in responses.filter(choice_value__isnull=False):
                    choice_text = response.choice_value.text
                    choice_counts[choice_text] = choice_counts.get(choice_text, 0) + 1
                question_data['choice_distribution'] = choice_counts

            elif question.question_type == 'YES_NO':
                yes_count = responses.filter(numeric_value=1).count()
                no_count = responses.filter(numeric_value=0).count()
                question_data.update({
                    'yes_count': yes_count,
                    'no_count': no_count,
                    'yes_percentage': (yes_count / (yes_count + no_count) * 100) if (yes_count + no_count) > 0 else 0,
                })

            analytics[str(question.id)] = question_data

        return analytics

    def _calculate_value_distribution(self, values, question):
        """Calculate distribution of numeric values."""
        distribution = {}

        if question.question_type == 'LIKERT_5':
            for i in range(1, 6):
                distribution[str(i)] = values.count(i)
        elif question.question_type == 'LIKERT_7':
            for i in range(1, 8):
                distribution[str(i)] = values.count(i)
        elif question.question_type == 'RATING_10':
            for i in range(1, 11):
                distribution[str(i)] = values.count(i)
        elif question.question_type == 'SCALE_CUSTOM' and question.scale_min and question.scale_max:
            for i in range(question.scale_min, question.scale_max + 1):
                distribution[str(i)] = values.count(i)

        return distribution


# Signal to create SurveyAnalytics when Survey is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=Survey)
def create_survey_analytics(sender, instance, created, **kwargs):
    if created:
        SurveyAnalytics.objects.create(survey=instance)
