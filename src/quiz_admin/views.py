from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.utils import timezone
from django.utils.text import slugify
from django.db.models import Q, Count, Avg
from django.contrib.auth.models import User
from django.db import models
from django.core.exceptions import PermissionDenied

from quiz_app.models import Quiz, Category, Question, QuizSession, UserAnswer, MCQChoice
from .models import QuizInvitation, QuizAnalytics, CategoryManager, QuizManager
import csv
import io
import re


class QuizAdminDashboard(LoginRequiredMixin, ListView):
    """Dashboard for quiz creators to manage their quizzes."""
    model = Quiz
    template_name = 'quiz_admin/dashboard.html'
    context_object_name = 'quizzes'
    paginate_by = 10
    
    def get_queryset(self):
        # Show quizzes owned by user or where user is a manager
        user = self.request.user

        # Get quizzes where user is a direct quiz manager
        managed_quiz_ids = QuizManager.objects.filter(
            user=user, status='ACCEPTED'
        ).values_list('quiz_id', flat=True)

        # Get quizzes in categories where user is a category manager
        managed_category_ids = CategoryManager.objects.filter(
            user=user, status='ACCEPTED'
        ).values_list('category_id', flat=True)

        # Combine all accessible quizzes
        accessible_quizzes = Quiz.objects.filter(
            Q(created_by=user) |
            Q(id__in=managed_quiz_ids) |
            Q(category_id__in=managed_category_ids)
        ).distinct().select_related('category').prefetch_related('analytics')

        return accessible_quizzes
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Dashboard statistics for current user
        user_quizzes = Quiz.objects.filter(created_by=self.request.user)
        total_quizzes = user_quizzes.count()
        published_quizzes = user_quizzes.filter(is_published=True).count()
        total_sessions = QuizSession.objects.filter(quiz__created_by=self.request.user).count()
        completed_sessions = QuizSession.objects.filter(quiz__created_by=self.request.user, is_complete=True).count()
        
        context.update({
            'total_quizzes': total_quizzes,
            'published_quizzes': published_quizzes,
            'draft_quizzes': total_quizzes - published_quizzes,
            'total_sessions': total_sessions,
            'completed_sessions': completed_sessions,
            'completion_rate': (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0,
        })
        
        return context


class QuizCreateView(LoginRequiredMixin, CreateView):
    """Create a new quiz."""
    model = Quiz
    template_name = 'quiz_admin/quiz_form.html'
    fields = [
        'title', 'description', 'category', 'random_order', 'max_questions',
        'time_limit', 'single_attempt', 'show_answers_at_end', 'show_correct_answers',
        'pass_mark', 'success_text', 'fail_text', 'is_published'
    ]

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # Show categories where user is owner or manager with create_quizzes permission
        user = self.request.user

        # Get categories where user is owner
        owned_categories = Category.objects.filter(created_by=user)

        # Get categories where user is a manager with create_quizzes permission
        managed_category_ids = CategoryManager.objects.filter(
            user=user, status='ACCEPTED', can_create_quizzes=True
        ).values_list('category_id', flat=True)

        accessible_categories = Category.objects.filter(
            Q(created_by=user) | Q(id__in=managed_category_ids)
        ).distinct()

        form.fields['category'].queryset = accessible_categories
        return form
    
    def form_valid(self, form):
        # Set the owner to the current user
        form.instance.created_by = self.request.user

        # Auto-generate slug from title
        form.instance.slug = slugify(form.instance.title)

        # Ensure unique slug for this user
        original_slug = form.instance.slug
        counter = 1
        while Quiz.objects.filter(slug=form.instance.slug, created_by=self.request.user).exists():
            form.instance.slug = f"{original_slug}-{counter}"
            counter += 1

        messages.success(self.request, f"Quiz '{form.instance.title}' created successfully!")
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse('quiz_admin:quiz_detail', kwargs={'pk': self.object.pk})


class QuizUpdateView(LoginRequiredMixin, UpdateView):
    """Update an existing quiz."""
    model = Quiz
    template_name = 'quiz_admin/quiz_form.html'
    fields = [
        'title', 'description', 'category', 'random_order', 'max_questions',
        'time_limit', 'single_attempt', 'show_answers_at_end', 'show_correct_answers',
        'pass_mark', 'success_text', 'fail_text', 'is_published'
    ]

    def get_queryset(self):
        # Allow editing quizzes where user has edit permissions
        user = self.request.user

        # Get quizzes where user is a direct quiz manager with edit permission
        managed_quiz_ids = QuizManager.objects.filter(
            user=user, status='ACCEPTED', can_edit_quiz=True
        ).values_list('quiz_id', flat=True)

        # Get quizzes in categories where user is a category manager with edit permission
        managed_category_ids = CategoryManager.objects.filter(
            user=user, status='ACCEPTED', can_edit_quizzes=True
        ).values_list('category_id', flat=True)

        return Quiz.objects.filter(
            Q(created_by=user) |
            Q(id__in=managed_quiz_ids) |
            Q(category_id__in=managed_category_ids)
        ).distinct()

    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # Show categories where user has management permissions
        user = self.request.user

        managed_category_ids = CategoryManager.objects.filter(
            user=user, status='ACCEPTED', can_edit_quizzes=True
        ).values_list('category_id', flat=True)

        accessible_categories = Category.objects.filter(
            Q(created_by=user) | Q(id__in=managed_category_ids)
        ).distinct()

        form.fields['category'].queryset = accessible_categories
        return form
    
    def form_valid(self, form):
        messages.success(self.request, f"Quiz '{form.instance.title}' updated successfully!")
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse('quiz_admin:quiz_detail', kwargs={'pk': self.object.pk})


class QuizAdminDetailView(LoginRequiredMixin, DetailView):
    """Detailed view of a quiz for management."""
    model = Quiz
    template_name = 'quiz_admin/quiz_detail.html'
    context_object_name = 'quiz'

    def get_queryset(self):
        # Allow viewing quizzes where user has management permissions
        user = self.request.user

        managed_quiz_ids = QuizManager.objects.filter(
            user=user, status='ACCEPTED'
        ).values_list('quiz_id', flat=True)

        managed_category_ids = CategoryManager.objects.filter(
            user=user, status='ACCEPTED'
        ).values_list('category_id', flat=True)

        return Quiz.objects.filter(
            Q(created_by=user) |
            Q(id__in=managed_quiz_ids) |
            Q(category_id__in=managed_category_ids)
        ).distinct()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        quiz = self.object
        
        # Get quiz analytics
        analytics, created = QuizAnalytics.objects.get_or_create(quiz=quiz)
        if created or not analytics.last_updated or \
           (timezone.now() - analytics.last_updated).total_seconds() > 3600:  # Update hourly
            analytics.update_analytics()
        
        # Get recent sessions
        recent_sessions = QuizSession.objects.filter(quiz=quiz).select_related('user').order_by('-started_at')[:10]
        
        # Get invitations
        invitations = QuizInvitation.objects.filter(quiz=quiz).order_by('-created_at')[:10]
        
        # Question statistics
        questions = quiz.questions.all()
        question_stats = []
        for question in questions:
            answers = UserAnswer.objects.filter(question=question)
            total_answers = answers.count()
            correct_answers = answers.filter(is_correct=True).count()
            accuracy = (correct_answers / total_answers * 100) if total_answers > 0 else 0
            
            question_stats.append({
                'question': question,
                'total_answers': total_answers,
                'correct_answers': correct_answers,
                'accuracy': accuracy
            })
        
        context.update({
            'analytics': analytics,
            'recent_sessions': recent_sessions,
            'invitations': invitations,
            'question_stats': question_stats,
        })
        
        return context


@login_required
def send_quiz_invitations(request, quiz_id):
    """Send invitations for a quiz."""
    quiz = get_object_or_404(Quiz, id=quiz_id, created_by=request.user)
    
    if request.method == 'POST':
        emails = request.POST.get('emails', '').strip()
        message = request.POST.get('message', '').strip()
        expires_days = int(request.POST.get('expires_days', 7))
        
        if not emails:
            messages.error(request, "Please provide at least one email address.")
            return redirect('quiz_admin:quiz_detail', pk=quiz_id)
        
        # Parse email addresses
        email_list = [email.strip() for email in emails.replace(',', '\n').split('\n') if email.strip()]
        
        # Validate emails and send invitations
        sent_count = 0
        error_count = 0
        
        for email in email_list:
            try:
                # Check if invitation already exists
                existing = QuizInvitation.objects.filter(quiz=quiz, email=email).first()
                if existing and existing.status == 'PENDING' and not existing.is_expired:
                    continue  # Skip if already invited and pending
                
                # Create invitation
                invitation = QuizInvitation.objects.create(
                    quiz=quiz,
                    invited_by=request.user,
                    email=email,
                    message=message,
                    expires_at=timezone.now() + timezone.timedelta(days=expires_days)
                )
                
                # Try to link to existing user
                try:
                    user = User.objects.get(email=email)
                    invitation.invited_user = user
                    invitation.save()
                except User.DoesNotExist:
                    pass
                
                sent_count += 1
                
            except Exception as e:
                error_count += 1
                continue
        
        if sent_count > 0:
            messages.success(request, f"Successfully sent {sent_count} invitation(s).")
        if error_count > 0:
            messages.warning(request, f"Failed to send {error_count} invitation(s).")
        
        return redirect('quiz_admin:quiz_detail', pk=quiz_id)
    
    context = {
        'quiz': quiz,
    }
    return render(request, 'quiz_admin/send_invitations.html', context)


@login_required
def quiz_analytics_view(request, quiz_id):
    """Detailed analytics view for a quiz."""
    quiz = get_object_or_404(Quiz, id=quiz_id, created_by=request.user)
    analytics, created = QuizAnalytics.objects.get_or_create(quiz=quiz)

    # Force update analytics
    analytics.update_analytics()

    # Get detailed session data
    sessions = QuizSession.objects.filter(quiz=quiz, is_complete=True).select_related('user')

    # Score distribution
    score_ranges = {
        '0-20%': 0, '21-40%': 0, '41-60%': 0, '61-80%': 0, '81-100%': 0
    }

    for session in sessions:
        score = session.score_percentage
        if score <= 20:
            score_ranges['0-20%'] += 1
        elif score <= 40:
            score_ranges['21-40%'] += 1
        elif score <= 60:
            score_ranges['41-60%'] += 1
        elif score <= 80:
            score_ranges['61-80%'] += 1
        else:
            score_ranges['81-100%'] += 1

    # Question difficulty analysis
    questions = quiz.questions.all()
    question_difficulty = []

    for question in questions:
        answers = UserAnswer.objects.filter(question=question)
        total = answers.count()
        correct = answers.filter(is_correct=True).count()
        accuracy = (correct / total * 100) if total > 0 else 0

        if accuracy >= 80:
            difficulty = 'Easy'
        elif accuracy >= 60:
            difficulty = 'Medium'
        elif accuracy >= 40:
            difficulty = 'Hard'
        else:
            difficulty = 'Very Hard'

        question_difficulty.append({
            'question': question,
            'total_answers': total,
            'correct_answers': correct,
            'accuracy': accuracy,
            'difficulty': difficulty
        })

    # Sort by difficulty (hardest first)
    question_difficulty.sort(key=lambda x: x['accuracy'])

    context = {
        'quiz': quiz,
        'analytics': analytics,
        'sessions': sessions,
        'score_ranges': score_ranges,
        'question_difficulty': question_difficulty,
    }

    return render(request, 'quiz_admin/analytics.html', context)


@login_required
def manage_invitations(request, quiz_id):
    """Manage invitations for a quiz."""
    quiz = get_object_or_404(Quiz, id=quiz_id, created_by=request.user)
    invitations = QuizInvitation.objects.filter(quiz=quiz).order_by('-created_at')

    # Handle invitation actions
    if request.method == 'POST':
        action = request.POST.get('action')
        invitation_id = request.POST.get('invitation_id')

        if action and invitation_id:
            try:
                invitation = QuizInvitation.objects.get(id=invitation_id, quiz=quiz)

                if action == 'resend':
                    # Reset invitation
                    invitation.status = 'PENDING'
                    invitation.expires_at = timezone.now() + timezone.timedelta(days=7)
                    invitation.save()
                    messages.success(request, f"Invitation resent to {invitation.email}")

                elif action == 'cancel':
                    invitation.status = 'EXPIRED'
                    invitation.save()
                    messages.success(request, f"Invitation to {invitation.email} cancelled")

            except QuizInvitation.DoesNotExist:
                messages.error(request, "Invitation not found")

        return redirect('quiz_admin:manage_invitations', quiz_id=quiz_id)

    context = {
        'quiz': quiz,
        'invitations': invitations,
    }

    return render(request, 'quiz_admin/manage_invitations.html', context)


class CategoryCreateView(LoginRequiredMixin, CreateView):
    """Create a new category."""
    model = Category
    template_name = 'quiz_admin/category_form.html'
    fields = ['name', 'description']

    def form_valid(self, form):
        # Set the owner to the current user
        form.instance.created_by = self.request.user

        # Auto-generate slug from name
        form.instance.slug = slugify(form.instance.name)

        # Ensure unique slug for this user
        original_slug = form.instance.slug
        counter = 1
        while Category.objects.filter(slug=form.instance.slug, created_by=self.request.user).exists():
            form.instance.slug = f"{original_slug}-{counter}"
            counter += 1

        messages.success(self.request, f"Category '{form.instance.name}' created successfully!")
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('quiz_admin:category_detail', kwargs={'pk': self.object.pk})


class CategoryListView(LoginRequiredMixin, ListView):
    """List categories that user owns or manages."""
    model = Category
    template_name = 'quiz_admin/category_list.html'
    context_object_name = 'categories'
    paginate_by = 12

    def get_queryset(self):
        # Show categories where user is owner or manager
        user = self.request.user

        # Get categories where user is a manager
        managed_category_ids = CategoryManager.objects.filter(
            user=user, status='ACCEPTED'
        ).values_list('category_id', flat=True)

        # Combine owned and managed categories
        accessible_categories = Category.objects.filter(
            Q(created_by=user) | Q(id__in=managed_category_ids)
        ).distinct().annotate(
            quiz_count=Count('quizzes'),
            published_quiz_count=Count('quizzes', filter=Q(quizzes__is_published=True))
        ).order_by('name')

        return accessible_categories


class CategoryDetailView(LoginRequiredMixin, DetailView):
    """Detailed view of a category for management."""
    model = Category
    template_name = 'quiz_admin/category_detail.html'
    context_object_name = 'category'

    def get_queryset(self):
        # Allow viewing categories where user has management permissions
        user = self.request.user

        managed_category_ids = CategoryManager.objects.filter(
            user=user, status='ACCEPTED'
        ).values_list('category_id', flat=True)

        return Category.objects.filter(
            Q(created_by=user) | Q(id__in=managed_category_ids)
        ).distinct()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        category = self.object
        user = self.request.user

        # Get user's permissions for this category
        permissions = category.get_user_permissions(user)
        context['permissions'] = permissions

        # Get quizzes in this category that user can see
        if permissions['can_edit_quizzes'] or category.created_by == user:
            quizzes = category.quizzes.all().order_by('-created_at')
        else:
            quizzes = category.quizzes.filter(is_published=True).order_by('-created_at')

        context['quizzes'] = quizzes

        # Get managers if user is owner
        if category.created_by == user:
            context['managers'] = CategoryManager.objects.filter(
                category=category
            ).select_related('user').order_by('-created_at')

        # Category statistics
        context['total_quizzes'] = category.quizzes.count()
        context['published_quizzes'] = category.quizzes.filter(is_published=True).count()
        context['total_questions'] = sum(quiz.total_questions for quiz in category.quizzes.all())

        return context


class CategoryUpdateView(LoginRequiredMixin, UpdateView):
    """Update an existing category."""
    model = Category
    template_name = 'quiz_admin/category_form.html'
    fields = ['name', 'description']

    def get_queryset(self):
        # Only allow editing categories owned by the current user
        return Category.objects.filter(created_by=self.request.user)

    def form_valid(self, form):
        # Update slug if name changed
        if 'name' in form.changed_data:
            form.instance.slug = slugify(form.instance.name)

            # Ensure unique slug for this user
            original_slug = form.instance.slug
            counter = 1
            while Category.objects.filter(
                slug=form.instance.slug,
                created_by=self.request.user
            ).exclude(pk=self.object.pk).exists():
                form.instance.slug = f"{original_slug}-{counter}"
                counter += 1

        messages.success(self.request, f"Category '{form.instance.name}' updated successfully!")
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('quiz_admin:category_detail', kwargs={'pk': self.object.pk})


@login_required
def invite_category_manager(request, category_id):
    """Invite a user to manage a category."""
    category = get_object_or_404(Category, id=category_id, created_by=request.user)

    if request.method == 'POST':
        email = request.POST.get('email', '').strip()
        permissions = {
            'can_create_quizzes': request.POST.get('can_create_quizzes') == 'on',
            'can_edit_quizzes': request.POST.get('can_edit_quizzes') == 'on',
            'can_invite_participants': request.POST.get('can_invite_participants') == 'on',
            'can_view_analytics': request.POST.get('can_view_analytics') == 'on',
            'can_manage_questions': request.POST.get('can_manage_questions') == 'on',
        }

        if not email:
            messages.error(request, "Please provide an email address.")
            return redirect('quiz_admin:invite_category_manager', category_id=category_id)

        try:
            # Find user by email
            user = User.objects.get(email=email)

            # Check if already a manager
            existing = CategoryManager.objects.filter(category=category, user=user).first()
            if existing:
                if existing.status == 'PENDING':
                    messages.warning(request, f"Invitation already sent to {email}")
                elif existing.status == 'ACCEPTED':
                    messages.warning(request, f"{email} is already a manager of this category")
                else:
                    # Update existing invitation
                    existing.status = 'PENDING'
                    existing.created_at = timezone.now()
                    for perm, value in permissions.items():
                        setattr(existing, perm, value)
                    existing.save()
                    messages.success(request, f"Manager invitation updated for {email}")
            else:
                # Create new manager invitation
                CategoryManager.objects.create(
                    category=category,
                    user=user,
                    invited_by=request.user,
                    **permissions
                )
                messages.success(request, f"Manager invitation sent to {email}")

        except User.DoesNotExist:
            messages.error(request, f"No user found with email {email}")

        return redirect('quiz_admin:category_managers', category_id=category_id)

    context = {
        'category': category,
    }
    return render(request, 'quiz_admin/invite_category_manager.html', context)


@login_required
def invite_quiz_manager(request, quiz_id):
    """Invite a user to manage a quiz."""
    quiz = get_object_or_404(Quiz, id=quiz_id)

    # Check if user can manage this quiz
    if not quiz.user_can_manage(request.user):
        raise PermissionDenied("You don't have permission to manage this quiz")

    if request.method == 'POST':
        email = request.POST.get('email', '').strip()
        permissions = {
            'can_edit_quiz': request.POST.get('can_edit_quiz') == 'on',
            'can_invite_participants': request.POST.get('can_invite_participants') == 'on',
            'can_view_analytics': request.POST.get('can_view_analytics') == 'on',
            'can_manage_questions': request.POST.get('can_manage_questions') == 'on',
        }

        if not email:
            messages.error(request, "Please provide an email address.")
            return redirect('quiz_admin:invite_quiz_manager', quiz_id=quiz_id)

        try:
            # Find user by email
            user = User.objects.get(email=email)

            # Check if already a manager
            existing = QuizManager.objects.filter(quiz=quiz, user=user).first()
            if existing:
                if existing.status == 'PENDING':
                    messages.warning(request, f"Invitation already sent to {email}")
                elif existing.status == 'ACCEPTED':
                    messages.warning(request, f"{email} is already a manager of this quiz")
                else:
                    # Update existing invitation
                    existing.status = 'PENDING'
                    existing.created_at = timezone.now()
                    for perm, value in permissions.items():
                        setattr(existing, perm, value)
                    existing.save()
                    messages.success(request, f"Manager invitation updated for {email}")
            else:
                # Create new manager invitation
                QuizManager.objects.create(
                    quiz=quiz,
                    user=user,
                    invited_by=request.user,
                    **permissions
                )
                messages.success(request, f"Manager invitation sent to {email}")

        except User.DoesNotExist:
            messages.error(request, f"No user found with email {email}")

        return redirect('quiz_admin:quiz_managers', quiz_id=quiz_id)

    context = {
        'quiz': quiz,
    }
    return render(request, 'quiz_admin/invite_quiz_manager.html', context)


@login_required
def category_managers(request, category_id):
    """Manage category managers."""
    category = get_object_or_404(Category, id=category_id, created_by=request.user)
    managers = CategoryManager.objects.filter(category=category).select_related('user').order_by('-created_at')

    # Handle manager actions
    if request.method == 'POST':
        action = request.POST.get('action')
        manager_id = request.POST.get('manager_id')

        if action and manager_id:
            try:
                manager = CategoryManager.objects.get(id=manager_id, category=category)

                if action == 'revoke':
                    manager.status = 'REVOKED'
                    manager.save()
                    messages.success(request, f"Manager access revoked for {manager.user.email}")

                elif action == 'delete':
                    manager.delete()
                    messages.success(request, f"Manager invitation deleted for {manager.user.email}")

            except CategoryManager.DoesNotExist:
                messages.error(request, "Manager not found")

        return redirect('quiz_admin:category_managers', category_id=category_id)

    context = {
        'category': category,
        'managers': managers,
    }
    return render(request, 'quiz_admin/category_managers.html', context)


@login_required
def quiz_managers(request, quiz_id):
    """Manage quiz managers."""
    quiz = get_object_or_404(Quiz, id=quiz_id)

    # Check if user can manage this quiz
    if not quiz.user_can_manage(request.user):
        raise PermissionDenied("You don't have permission to manage this quiz")

    managers = QuizManager.objects.filter(quiz=quiz).select_related('user').order_by('-created_at')

    # Handle manager actions
    if request.method == 'POST':
        action = request.POST.get('action')
        manager_id = request.POST.get('manager_id')

        if action and manager_id:
            try:
                manager = QuizManager.objects.get(id=manager_id, quiz=quiz)

                if action == 'revoke':
                    manager.status = 'REVOKED'
                    manager.save()
                    messages.success(request, f"Manager access revoked for {manager.user.email}")

                elif action == 'delete':
                    manager.delete()
                    messages.success(request, f"Manager invitation deleted for {manager.user.email}")

            except QuizManager.DoesNotExist:
                messages.error(request, "Manager not found")

        return redirect('quiz_admin:quiz_managers', quiz_id=quiz_id)

    context = {
        'quiz': quiz,
        'managers': managers,
    }
    return render(request, 'quiz_admin/quiz_managers.html', context)


# ============================================================================
# QUESTION MANAGEMENT VIEWS
# ============================================================================

@login_required
def quiz_questions(request, quiz_id):
    """Manage questions for a quiz."""
    quiz = get_object_or_404(Quiz, id=quiz_id)

    # Check if user can manage questions for this quiz
    permissions = quiz.get_user_permissions(request.user)
    if not permissions['can_manage_questions']:
        raise PermissionDenied("You don't have permission to manage questions for this quiz")

    questions = quiz.questions.all().order_by('created_at')

    context = {
        'quiz': quiz,
        'questions': questions,
        'permissions': permissions,
    }

    return render(request, 'quiz_admin/quiz_questions.html', context)


@login_required
def add_question_modal(request, quiz_id):
    """Add a single question via modal form."""
    quiz = get_object_or_404(Quiz, id=quiz_id)

    # Check permissions
    permissions = quiz.get_user_permissions(request.user)
    if not permissions['can_manage_questions']:
        raise PermissionDenied("You don't have permission to manage questions for this quiz")

    if request.method == 'POST':
        question_type = request.POST.get('question_type')
        content = request.POST.get('content', '').strip()
        explanation = request.POST.get('explanation', '').strip()
        marks = int(request.POST.get('marks', 1))

        if not content:
            messages.error(request, "Question content is required.")
            return redirect('quiz_admin:quiz_questions', quiz_id=quiz_id)

        # Create question
        question = Question.objects.create(
            question_type=question_type,
            content=content,
            explanation=explanation,
            marks=marks,
            category=quiz.category,
            created_by=request.user
        )

        # Add to quiz
        question.quiz.add(quiz)

        # Handle choices based on question type
        if question_type == 'MCQ':
            choices_data = []
            for i in range(1, 6):  # Support up to 5 choices
                choice_content = request.POST.get(f'choice_{i}', '').strip()
                if choice_content:
                    is_correct = request.POST.get(f'correct_{i}') == 'on'
                    choices_data.append({
                        'content': choice_content,
                        'is_correct': is_correct,
                        'order': i
                    })

            # Validate at least one correct answer
            if not any(choice['is_correct'] for choice in choices_data):
                question.delete()
                messages.error(request, "At least one choice must be marked as correct.")
                return redirect('quiz_admin:quiz_questions', quiz_id=quiz_id)

            # Create choices
            for choice_data in choices_data:
                MCQChoice.objects.create(
                    question=question,
                    **choice_data
                )

        elif question_type == 'TF':
            # Create True/False choices
            correct_answer = request.POST.get('tf_answer') == 'true'
            MCQChoice.objects.create(
                question=question,
                content='True',
                is_correct=correct_answer,
                order=1
            )
            MCQChoice.objects.create(
                question=question,
                content='False',
                is_correct=not correct_answer,
                order=2
            )

        messages.success(request, f"Question added successfully!")
        return redirect('quiz_admin:quiz_questions', quiz_id=quiz_id)

    context = {
        'quiz': quiz,
    }

    return render(request, 'quiz_admin/add_question_modal.html', context)


@login_required
def bulk_add_questions(request, quiz_id):
    """Add questions via text parsing or CSV upload."""
    quiz = get_object_or_404(Quiz, id=quiz_id)

    # Check permissions
    permissions = quiz.get_user_permissions(request.user)
    if not permissions['can_manage_questions']:
        raise PermissionDenied("You don't have permission to manage questions for this quiz")

    if request.method == 'POST':
        method = request.POST.get('method')

        if method == 'text':
            return handle_text_questions(request, quiz)
        elif method == 'csv':
            return handle_csv_questions(request, quiz)

    context = {
        'quiz': quiz,
    }

    return render(request, 'quiz_admin/bulk_add_questions.html', context)


def handle_text_questions(request, quiz):
    """Parse and create questions from text input."""
    text_content = request.POST.get('text_content', '').strip()

    if not text_content:
        messages.error(request, "Please provide question text.")
        return redirect('quiz_admin:bulk_add_questions', quiz_id=quiz.id)

    try:
        questions_created = parse_text_questions(text_content, quiz, request.user)
        messages.success(request, f"Successfully created {questions_created} question(s)!")
    except Exception as e:
        messages.error(request, f"Error parsing questions: {str(e)}")

    return redirect('quiz_admin:quiz_questions', quiz_id=quiz.id)


def handle_csv_questions(request, quiz):
    """Parse and create questions from CSV upload."""
    csv_file = request.FILES.get('csv_file')

    if not csv_file:
        messages.error(request, "Please upload a CSV file.")
        return redirect('quiz_admin:bulk_add_questions', quiz_id=quiz.id)

    if not csv_file.name.endswith('.csv'):
        messages.error(request, "Please upload a valid CSV file.")
        return redirect('quiz_admin:bulk_add_questions', quiz_id=quiz.id)

    try:
        questions_created = parse_csv_questions(csv_file, quiz, request.user)
        messages.success(request, f"Successfully created {questions_created} question(s) from CSV!")
    except Exception as e:
        messages.error(request, f"Error parsing CSV: {str(e)}")

    return redirect('quiz_admin:quiz_questions', quiz_id=quiz.id)


def parse_text_questions(text_content, quiz, user):
    """Parse questions from formatted text."""
    questions_created = 0

    # Split by question blocks (MC:, SA:, TF:)
    blocks = re.split(r'\n(?=(?:MC|SA|TF):)', text_content.strip())

    for block in blocks:
        block = block.strip()
        if not block:
            continue

        lines = [line.strip() for line in block.split('\n') if line.strip()]
        if len(lines) < 2:
            continue

        # Parse question type
        first_line = lines[0]
        if first_line.startswith('MC:'):
            question_type = 'MCQ'
            question_content = first_line[3:].strip()
        elif first_line.startswith('SA:'):
            question_type = 'SUBJECTIVE'
            question_content = first_line[3:].strip()
        elif first_line.startswith('TF:'):
            question_type = 'TF'
            question_content = first_line[3:].strip()
        else:
            continue

        # Extract question content if it's in brackets
        if question_content.startswith('[') and ']' in question_content:
            end_bracket = question_content.find(']')
            question_content = question_content[1:end_bracket]
            remaining_lines = lines[1:]
        else:
            # Question content is on the next line
            if len(lines) < 2:
                continue
            question_content = lines[1]
            remaining_lines = lines[2:]

        # Create question
        question = Question.objects.create(
            question_type=question_type,
            content=question_content,
            category=quiz.category,
            created_by=user,
            marks=1
        )
        question.quiz.add(quiz)

        # Handle choices based on type
        if question_type == 'MCQ':
            for i, choice_text in enumerate(remaining_lines):
                if choice_text:
                    # First choice is correct by default
                    is_correct = (i == 0)
                    MCQChoice.objects.create(
                        question=question,
                        content=choice_text,
                        is_correct=is_correct,
                        order=i + 1
                    )

        elif question_type == 'TF':
            # Parse TRUE/FALSE from remaining lines
            correct_answer = True  # Default
            if remaining_lines:
                answer_text = remaining_lines[0].upper()
                correct_answer = answer_text in ['TRUE', 'T', 'YES', 'Y']

            MCQChoice.objects.create(
                question=question,
                content='True',
                is_correct=correct_answer,
                order=1
            )
            MCQChoice.objects.create(
                question=question,
                content='False',
                is_correct=not correct_answer,
                order=2
            )

        questions_created += 1

    return questions_created


def parse_csv_questions(csv_file, quiz, user):
    """Parse questions from CSV file."""
    questions_created = 0

    # Read CSV content
    content = csv_file.read().decode('utf-8')
    csv_reader = csv.DictReader(io.StringIO(content))

    for row in csv_reader:
        question_type = row.get('type', '').upper()
        question_content = row.get('question', '').strip()
        explanation = row.get('explanation', '').strip()
        marks = int(row.get('marks', 1))

        if not question_content:
            continue

        # Map CSV types to model types
        if question_type in ['MC', 'MCQ', 'MULTIPLE_CHOICE']:
            question_type = 'MCQ'
        elif question_type in ['TF', 'TRUE_FALSE', 'TRUEFALSE']:
            question_type = 'TF'
        elif question_type in ['SA', 'SHORT_ANSWER', 'SUBJECTIVE']:
            question_type = 'SUBJECTIVE'
        else:
            continue

        # Create question
        question = Question.objects.create(
            question_type=question_type,
            content=question_content,
            explanation=explanation,
            marks=marks,
            category=quiz.category,
            created_by=user
        )
        question.quiz.add(quiz)

        # Handle choices
        if question_type == 'MCQ':
            choices = []
            for i in range(1, 6):  # Support up to 5 choices
                choice_key = f'choice_{i}'
                correct_key = f'correct_{i}'

                choice_content = row.get(choice_key, '').strip()
                if choice_content:
                    is_correct = row.get(correct_key, '').lower() in ['true', '1', 'yes', 'y']
                    choices.append({
                        'content': choice_content,
                        'is_correct': is_correct,
                        'order': i
                    })

            # Create choices
            for choice_data in choices:
                MCQChoice.objects.create(
                    question=question,
                    **choice_data
                )

        elif question_type == 'TF':
            correct_answer = row.get('correct_answer', 'true').lower() in ['true', '1', 'yes', 'y']

            MCQChoice.objects.create(
                question=question,
                content='True',
                is_correct=correct_answer,
                order=1
            )
            MCQChoice.objects.create(
                question=question,
                content='False',
                is_correct=not correct_answer,
                order=2
            )

        questions_created += 1

    return questions_created
