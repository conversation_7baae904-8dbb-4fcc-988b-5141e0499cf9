from django.urls import path
from . import views

app_name = 'quiz_admin'

urlpatterns = [
    # Quiz Admin Dashboard
    path('', views.QuizAdminDashboard.as_view(), name='dashboard'),

    # Category Management
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('category/create/', views.CategoryCreateView.as_view(), name='category_create'),
    path('category/<int:pk>/', views.CategoryDetailView.as_view(), name='category_detail'),
    path('category/<int:pk>/edit/', views.CategoryUpdateView.as_view(), name='category_edit'),
    path('category/<int:category_id>/managers/', views.category_managers, name='category_managers'),
    path('category/<int:category_id>/invite-manager/', views.invite_category_manager, name='invite_category_manager'),

    # Quiz Management
    path('quiz/create/', views.QuizCreateView.as_view(), name='quiz_create'),
    path('quiz/<int:pk>/', views.QuizAdminDetailView.as_view(), name='quiz_detail'),
    path('quiz/<int:pk>/edit/', views.QuizUpdateView.as_view(), name='quiz_edit'),
    path('quiz/<int:quiz_id>/managers/', views.quiz_managers, name='quiz_managers'),
    path('quiz/<int:quiz_id>/invite-manager/', views.invite_quiz_manager, name='invite_quiz_manager'),

    # Invitation Management
    path('quiz/<int:quiz_id>/invite/', views.send_quiz_invitations, name='send_invitations'),
    path('quiz/<int:quiz_id>/invitations/', views.manage_invitations, name='manage_invitations'),

    # Analytics
    path('quiz/<int:quiz_id>/analytics/', views.quiz_analytics_view, name='analytics'),
]
