from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from quiz_app.models import Quiz, Question
import uuid


class QuizInvitation(models.Model):
    """Invitations sent to users for specific quizzes."""
    INVITATION_STATUS = [
        ('PENDING', 'Pending'),
        ('ACCEPTED', 'Accepted'),
        ('DECLINED', 'Declined'),
        ('EXPIRED', 'Expired'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='invitations')
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_invitations')
    invited_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_invitations', null=True, blank=True)
    email = models.EmailField(help_text="Email address for invitation")
    
    # Invitation details
    message = models.TextField(blank=True, help_text="Personal message with invitation")
    status = models.CharField(max_length=20, choices=INVITATION_STATUS, default='PENDING')
    
    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(help_text="When invitation expires")
    responded_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['quiz', 'email']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Invitation to {self.email} for {self.quiz.title}"
    
    @property
    def is_expired(self):
        return timezone.now() > self.expires_at
    
    def accept(self):
        """Accept the invitation."""
        if not self.is_expired and self.status == 'PENDING':
            self.status = 'ACCEPTED'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False
    
    def decline(self):
        """Decline the invitation."""
        if not self.is_expired and self.status == 'PENDING':
            self.status = 'DECLINED'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False


class QuizAnalytics(models.Model):
    """Analytics and tracking for quiz performance."""
    quiz = models.OneToOneField(Quiz, on_delete=models.CASCADE, related_name='analytics')
    
    # Participation stats
    total_invitations_sent = models.PositiveIntegerField(default=0)
    total_participants = models.PositiveIntegerField(default=0)
    total_completions = models.PositiveIntegerField(default=0)
    
    # Performance stats
    average_score = models.FloatField(default=0.0)
    highest_score = models.FloatField(default=0.0)
    lowest_score = models.FloatField(default=0.0)
    average_completion_time = models.DurationField(null=True, blank=True)
    
    # Question analytics
    most_difficult_question = models.ForeignKey(
        Question, 
        on_delete=models.SET_NULL, 
        null=True, blank=True,
        related_name='analytics_most_difficult'
    )
    easiest_question = models.ForeignKey(
        Question, 
        on_delete=models.SET_NULL, 
        null=True, blank=True,
        related_name='analytics_easiest'
    )
    
    # Timing
    last_updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Analytics for {self.quiz.title}"
    
    def update_analytics(self):
        """Recalculate analytics from quiz sessions."""
        from quiz_app.models import QuizSession
        
        sessions = QuizSession.objects.filter(quiz=self.quiz, is_complete=True)
        
        if sessions.exists():
            self.total_participants = sessions.values('user').distinct().count()
            self.total_completions = sessions.count()
            
            # Score statistics
            scores = [s.score_percentage for s in sessions]
            self.average_score = sum(scores) / len(scores)
            self.highest_score = max(scores)
            self.lowest_score = min(scores)
            
            # Time statistics
            completion_times = [s.time_spent for s in sessions if s.time_spent]
            if completion_times:
                total_seconds = sum(t.total_seconds() for t in completion_times)
                avg_seconds = total_seconds / len(completion_times)
                self.average_completion_time = timezone.timedelta(seconds=avg_seconds)
        
        # Update invitation count
        self.total_invitations_sent = self.quiz.invitations.count()
        
        self.save()


# Signals to create analytics when quiz is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=Quiz)
def create_quiz_analytics(sender, instance, created, **kwargs):
    if created:
        QuizAnalytics.objects.create(quiz=instance)


class CategoryManager(models.Model):
    """Managers who can help manage specific categories."""
    category = models.ForeignKey('quiz_app.Category', on_delete=models.CASCADE, related_name='managers')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='managed_categories')
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='category_manager_invitations')

    # Permissions
    can_create_quizzes = models.BooleanField(default=True, help_text="Can create new quizzes in this category")
    can_edit_quizzes = models.BooleanField(default=True, help_text="Can edit existing quizzes in this category")
    can_invite_participants = models.BooleanField(default=True, help_text="Can send quiz invitations")
    can_view_analytics = models.BooleanField(default=True, help_text="Can view quiz analytics")
    can_manage_questions = models.BooleanField(default=False, help_text="Can create/edit questions")

    # Status
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('ACCEPTED', 'Accepted'),
        ('DECLINED', 'Declined'),
        ('REVOKED', 'Revoked'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')

    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    responded_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ['category', 'user']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} managing {self.category.name}"

    def accept(self):
        """Accept the manager invitation."""
        if self.status == 'PENDING':
            self.status = 'ACCEPTED'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False

    def decline(self):
        """Decline the manager invitation."""
        if self.status == 'PENDING':
            self.status = 'DECLINED'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False


class QuizManager(models.Model):
    """Managers who can help manage specific quizzes."""
    quiz = models.ForeignKey('quiz_app.Quiz', on_delete=models.CASCADE, related_name='managers')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='managed_quizzes')
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='quiz_manager_invitations')

    # Permissions
    can_edit_quiz = models.BooleanField(default=True, help_text="Can edit quiz settings")
    can_invite_participants = models.BooleanField(default=True, help_text="Can send quiz invitations")
    can_view_analytics = models.BooleanField(default=True, help_text="Can view quiz analytics")
    can_manage_questions = models.BooleanField(default=False, help_text="Can create/edit questions for this quiz")

    # Status
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('ACCEPTED', 'Accepted'),
        ('DECLINED', 'Declined'),
        ('REVOKED', 'Revoked'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')

    # Timing
    created_at = models.DateTimeField(auto_now_add=True)
    responded_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ['quiz', 'user']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} managing {self.quiz.title}"

    def accept(self):
        """Accept the manager invitation."""
        if self.status == 'PENDING':
            self.status = 'ACCEPTED'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False

    def decline(self):
        """Decline the manager invitation."""
        if self.status == 'PENDING':
            self.status = 'DECLINED'
            self.responded_at = timezone.now()
            self.save()
            return True
        return False
