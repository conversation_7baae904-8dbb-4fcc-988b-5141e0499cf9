from django.contrib import admin
from .models import QuizInvitation, QuizAnalytics


@admin.register(QuizInvitation)
class QuizInvitationAdmin(admin.ModelAdmin):
    list_display = ['email', 'quiz', 'status', 'invited_by', 'created_at', 'expires_at']
    list_filter = ['status', 'created_at', 'expires_at']
    search_fields = ['email', 'quiz__title', 'invited_by__email']
    readonly_fields = ['id', 'created_at', 'responded_at']
    
    fieldsets = (
        ('Invitation Details', {
            'fields': ('quiz', 'invited_by', 'email', 'invited_user', 'message')
        }),
        ('Status', {
            'fields': ('status', 'expires_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'responded_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(QuizAnalytics)
class QuizAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['quiz', 'total_participants', 'total_completions', 'average_score', 'last_updated']
    list_filter = ['last_updated']
    search_fields = ['quiz__title']
    readonly_fields = ['last_updated']
    
    fieldsets = (
        ('Quiz', {
            'fields': ('quiz',)
        }),
        ('Participation Stats', {
            'fields': ('total_invitations_sent', 'total_participants', 'total_completions')
        }),
        ('Performance Stats', {
            'fields': ('average_score', 'highest_score', 'lowest_score', 'average_completion_time')
        }),
        ('Question Analytics', {
            'fields': ('most_difficult_question', 'easiest_question')
        }),
        ('Metadata', {
            'fields': ('last_updated',),
            'classes': ('collapse',)
        }),
    )
