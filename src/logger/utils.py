import logging
import json
from datetime import datetime
from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone
from typing import Optional, Dict, Any


class AppLogger:
    """Centralized logging utility for the QuizLog application."""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # Create console handler if not exists
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def info(self, message: str, user: Optional[User] = None, extra_data: Optional[Dict[str, Any]] = None):
        """Log info level message."""
        log_data = self._prepare_log_data(message, user, extra_data)
        self.logger.info(json.dumps(log_data))
    
    def warning(self, message: str, user: Optional[User] = None, extra_data: Optional[Dict[str, Any]] = None):
        """Log warning level message."""
        log_data = self._prepare_log_data(message, user, extra_data)
        self.logger.warning(json.dumps(log_data))
    
    def error(self, message: str, user: Optional[User] = None, extra_data: Optional[Dict[str, Any]] = None, exception: Optional[Exception] = None):
        """Log error level message."""
        log_data = self._prepare_log_data(message, user, extra_data)
        if exception:
            log_data['exception'] = str(exception)
            log_data['exception_type'] = type(exception).__name__
        self.logger.error(json.dumps(log_data))
    
    def debug(self, message: str, user: Optional[User] = None, extra_data: Optional[Dict[str, Any]] = None):
        """Log debug level message."""
        if settings.DEBUG:
            log_data = self._prepare_log_data(message, user, extra_data)
            self.logger.debug(json.dumps(log_data))
    
    def _prepare_log_data(self, message: str, user: Optional[User], extra_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Prepare structured log data."""
        log_data = {
            'timestamp': timezone.now().isoformat(),
            'message': message,
        }
        
        if user:
            log_data['user'] = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
            }
        
        if extra_data:
            log_data['extra'] = extra_data
        
        return log_data


# Pre-configured loggers for different apps
quiz_logger = AppLogger('quiz_app')
survey_logger = AppLogger('survey')
admin_logger = AppLogger('quiz_admin')
question_logger = AppLogger('question_manager')


def log_user_action(action: str, user: User, resource_type: str, resource_id: Optional[int] = None, extra_data: Optional[Dict[str, Any]] = None):
    """Log user actions across the application."""
    logger = AppLogger('user_actions')
    
    log_data = {
        'action': action,
        'resource_type': resource_type,
        'resource_id': resource_id,
    }
    
    if extra_data:
        log_data.update(extra_data)
    
    logger.info(f"User action: {action}", user=user, extra_data=log_data)


def log_survey_event(event: str, survey_id: int, user: Optional[User] = None, extra_data: Optional[Dict[str, Any]] = None):
    """Log survey-specific events."""
    log_data = {
        'event': event,
        'survey_id': survey_id,
    }
    
    if extra_data:
        log_data.update(extra_data)
    
    survey_logger.info(f"Survey event: {event}", user=user, extra_data=log_data)


def log_ai_analysis(analysis_type: str, input_data: Dict[str, Any], output_data: Dict[str, Any], user: Optional[User] = None):
    """Log AI analysis operations."""
    logger = AppLogger('ai_analysis')
    
    log_data = {
        'analysis_type': analysis_type,
        'input_summary': {
            'data_points': len(input_data.get('responses', [])),
            'survey_type': input_data.get('survey_type'),
        },
        'output_summary': {
            'insights_generated': len(output_data.get('insights', [])),
            'score': output_data.get('overall_score'),
        }
    }
    
    logger.info(f"AI analysis completed: {analysis_type}", user=user, extra_data=log_data)


def log_error_with_context(error: Exception, context: str, user: Optional[User] = None, extra_data: Optional[Dict[str, Any]] = None):
    """Log errors with additional context."""
    logger = AppLogger('error_tracking')
    
    error_data = {
        'context': context,
        'error_type': type(error).__name__,
        'error_message': str(error),
    }
    
    if extra_data:
        error_data.update(extra_data)
    
    logger.error(f"Error in {context}", user=user, extra_data=error_data, exception=error)
