{% extends "base.html" %}
{% load static %}

{% block title %}Analytics - {{ survey.title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-base-content mb-2">Survey Analytics</h1>
            <p class="text-base-content/70">{{ survey.title }}</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <a href="{% url 'survey:survey_detail' slug=survey.slug %}" class="btn btn-outline">
                <i class="fas fa-arrow-left mr-2"></i>Back to Survey
            </a>
            <a href="{% url 'survey:survey_responses' slug=survey.slug %}" class="btn btn-primary">
                <i class="fas fa-list mr-2"></i>View Responses
            </a>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="stat bg-base-100 shadow-lg rounded-lg">
            <div class="stat-figure text-primary">
                <i class="fas fa-users text-3xl"></i>
            </div>
            <div class="stat-title">Total Responses</div>
            <div class="stat-value text-primary">{{ analytics.total_responses|default:0 }}</div>
            <div class="stat-desc">{{ analytics.completed_responses|default:0 }} completed</div>
        </div>

        <div class="stat bg-base-100 shadow-lg rounded-lg">
            <div class="stat-figure text-secondary">
                <i class="fas fa-chart-line text-3xl"></i>
            </div>
            <div class="stat-title">Average Score</div>
            <div class="stat-value text-secondary">{{ analytics.average_score|floatformat:1|default:"0.0" }}%</div>
            <div class="stat-desc">
                Range: {{ analytics.lowest_score|floatformat:1|default:"0" }}% - {{ analytics.highest_score|floatformat:1|default:"0" }}%
            </div>
        </div>

        <div class="stat bg-base-100 shadow-lg rounded-lg">
            <div class="stat-figure text-accent">
                <i class="fas fa-clock text-3xl"></i>
            </div>
            <div class="stat-title">Avg. Time</div>
            <div class="stat-value text-accent">
                {% if analytics.average_completion_time %}
                {{ analytics.average_completion_time.total_seconds|floatformat:0 }}s
                {% else %}
                -
                {% endif %}
            </div>
            <div class="stat-desc">
                {% if analytics.median_completion_time %}
                Median: {{ analytics.median_completion_time.total_seconds|floatformat:0 }}s
                {% endif %}
            </div>
        </div>

        <div class="stat bg-base-100 shadow-lg rounded-lg">
            <div class="stat-figure text-info">
                <i class="fas fa-user-secret text-3xl"></i>
            </div>
            <div class="stat-title">Anonymous</div>
            <div class="stat-value text-info">{{ analytics.anonymous_responses|default:0 }}</div>
            <div class="stat-desc">
                {{ analytics.anonymous_responses|default:0|div:analytics.total_responses|default:1|mul:100|floatformat:0 }}% of total
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Score Distribution -->
        {% if analytics.score_distribution %}
        <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
                <h3 class="card-title mb-4">
                    <i class="fas fa-chart-bar mr-2"></i>Score Distribution
                </h3>
                
                <div class="space-y-4">
                    {% for range_name, range_data in analytics.score_distribution.items %}
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium capitalize">
                                {% if range_name == 'excellent' %}
                                <i class="fas fa-star text-yellow-500 mr-1"></i>Excellent
                                {% elif range_name == 'good' %}
                                <i class="fas fa-thumbs-up text-green-500 mr-1"></i>Good
                                {% elif range_name == 'average' %}
                                <i class="fas fa-minus text-blue-500 mr-1"></i>Average
                                {% elif range_name == 'below_average' %}
                                <i class="fas fa-arrow-down text-orange-500 mr-1"></i>Below Average
                                {% elif range_name == 'poor' %}
                                <i class="fas fa-exclamation-triangle text-red-500 mr-1"></i>Poor
                                {% endif %}
                                ({{ range_data.min|floatformat:0 }}-{{ range_data.max|floatformat:0 }}%)
                            </span>
                            <span class="text-sm">{{ range_data.count }} responses</span>
                        </div>
                        <progress class="progress progress-primary w-full" 
                                 value="{{ range_data.count }}" 
                                 max="{{ analytics.completed_responses|default:1 }}"></progress>
                        <div class="text-xs text-base-content/70 mt-1">
                            {{ range_data.count|div:analytics.completed_responses|default:1|mul:100|floatformat:1 }}% of responses
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Question Analytics -->
        {% if analytics.question_analytics %}
        <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
                <h3 class="card-title mb-4">
                    <i class="fas fa-question-circle mr-2"></i>Question Performance
                </h3>
                
                <div class="space-y-4 max-h-96 overflow-y-auto">
                    {% for question_id, question_data in analytics.question_analytics.items %}
                    <div class="border border-base-300 rounded-lg p-3">
                        <div class="flex justify-between items-start mb-2">
                            <h4 class="text-sm font-medium line-clamp-2">
                                Question {{ forloop.counter }}
                            </h4>
                            <div class="badge badge-outline badge-sm">
                                {{ question_data.response_rate|floatformat:0 }}% response rate
                            </div>
                        </div>
                        
                        {% if question_data.average %}
                        <div class="text-xs text-base-content/70 mb-2">
                            Average: {{ question_data.average|floatformat:1 }}
                            (Range: {{ question_data.min|floatformat:1 }} - {{ question_data.max|floatformat:1 }})
                        </div>
                        {% endif %}
                        
                        {% if question_data.yes_percentage %}
                        <div class="text-xs text-base-content/70">
                            Yes: {{ question_data.yes_percentage|floatformat:1 }}%
                        </div>
                        {% endif %}
                        
                        {% if question_data.choice_distribution %}
                        <div class="text-xs">
                            <span class="text-base-content/70">Top choice:</span>
                            {% for choice, count in question_data.choice_distribution.items %}
                            {% if forloop.first %}
                            {{ choice }} ({{ count }})
                            {% endif %}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Recent Responses -->
    {% if recent_responses %}
    <div class="card bg-base-100 shadow-lg mt-8">
        <div class="card-body">
            <h3 class="card-title mb-4">
                <i class="fas fa-clock mr-2"></i>Recent Responses
            </h3>
            
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>Respondent</th>
                            <th>Score</th>
                            <th>Performance Level</th>
                            <th>Completed</th>
                            <th>Time Taken</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for session in recent_responses %}
                        <tr>
                            <td>
                                {% if session.user %}
                                <div class="flex items-center">
                                    <div class="avatar placeholder mr-3">
                                        <div class="bg-neutral-focus text-neutral-content rounded-full w-8">
                                            <span class="text-xs">{{ session.user.username|first|upper }}</span>
                                        </div>
                                    </div>
                                    {{ session.user.username }}
                                </div>
                                {% else %}
                                <span class="text-base-content/60">
                                    <i class="fas fa-user-secret mr-1"></i>Anonymous
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if session.total_score %}
                                <div class="flex items-center">
                                    <span class="font-medium">{{ session.score_percentage|floatformat:1 }}%</span>
                                    <div class="ml-2 w-16">
                                        <progress class="progress progress-primary progress-xs" 
                                                 value="{{ session.score_percentage }}" max="100"></progress>
                                    </div>
                                </div>
                                {% else %}
                                <span class="text-base-content/60">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if session.ai_analysis_results.performance_level %}
                                <div class="badge badge-sm
                                    {% if session.ai_analysis_results.performance_level == 'Exceptional' %}badge-success
                                    {% elif session.ai_analysis_results.performance_level == 'Excellent' %}badge-primary
                                    {% elif session.ai_analysis_results.performance_level == 'Good' %}badge-info
                                    {% elif session.ai_analysis_results.performance_level == 'Satisfactory' %}badge-warning
                                    {% else %}badge-error{% endif %}">
                                    {{ session.ai_analysis_results.performance_level }}
                                </div>
                                {% else %}
                                <span class="text-base-content/60">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-sm">{{ session.completed_at|date:"M d, H:i" }}</span>
                            </td>
                            <td>
                                {% if session.time_spent %}
                                <span class="text-sm">{{ session.time_spent }}</span>
                                {% else %}
                                <span class="text-base-content/60">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'survey:survey_results' session_id=session.id %}" 
                                   class="btn btn-ghost btn-xs">
                                    <i class="fas fa-eye mr-1"></i>View
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="text-center mt-4">
                <a href="{% url 'survey:survey_responses' slug=survey.slug %}" 
                   class="btn btn-outline btn-sm">
                    View All Responses
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- AI Insights Summary -->
    {% if analytics.ai_insights %}
    <div class="card bg-gradient-to-r from-info/10 to-primary/10 border border-info/20 mt-8">
        <div class="card-body">
            <h3 class="card-title text-info mb-4">
                <i class="fas fa-robot mr-2"></i>AI Insights Summary
            </h3>
            
            {% if analytics.ai_summary %}
            <div class="prose max-w-none">
                <p>{{ analytics.ai_summary }}</p>
            </div>
            {% endif %}
            
            {% if analytics.ai_insights.common_strengths %}
            <div class="mt-4">
                <h4 class="font-semibold text-success mb-2">Common Strengths:</h4>
                <ul class="list-disc list-inside text-sm space-y-1">
                    {% for strength in analytics.ai_insights.common_strengths %}
                    <li>{{ strength }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
            
            {% if analytics.ai_insights.common_improvements %}
            <div class="mt-4">
                <h4 class="font-semibold text-warning mb-2">Common Areas for Improvement:</h4>
                <ul class="list-disc list-inside text-sm space-y-1">
                    {% for improvement in analytics.ai_insights.common_improvements %}
                    <li>{{ improvement }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Export Options -->
    <div class="card bg-base-100 shadow-lg mt-8">
        <div class="card-body">
            <h3 class="card-title mb-4">
                <i class="fas fa-download mr-2"></i>Export Data
            </h3>
            
            <div class="flex flex-wrap gap-3">
                <button class="btn btn-outline btn-sm" onclick="exportToCSV()">
                    <i class="fas fa-file-csv mr-2"></i>Export to CSV
                </button>
                <button class="btn btn-outline btn-sm" onclick="window.print()">
                    <i class="fas fa-print mr-2"></i>Print Report
                </button>
                <button class="btn btn-outline btn-sm" onclick="generatePDF()">
                    <i class="fas fa-file-pdf mr-2"></i>Generate PDF
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportToCSV() {
    // Implement CSV export functionality
    alert('CSV export functionality would be implemented here');
}

function generatePDF() {
    // Implement PDF generation functionality
    alert('PDF generation functionality would be implemented here');
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

@media print {
    .btn, .navbar, .footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
        break-inside: avoid;
    }
}
</style>
{% endblock %}
