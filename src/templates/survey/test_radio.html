<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Radio Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.10/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-6">Radio Button Test</h1>
        
        <!-- Test 1: Basic DaisyUI Radio -->
        <div class="card bg-base-100 shadow-lg mb-6">
            <div class="card-body">
                <h2 class="card-title">Test 1: Basic DaisyUI Radio</h2>
                <form>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Option 1</span>
                            <input type="radio" name="test1" class="radio" value="1" />
                        </label>
                    </div>
                    <div class="form-control">
                        <label class="label cursor-pointer">
                            <span class="label-text">Option 2</span>
                            <input type="radio" name="test1" class="radio" value="2" />
                        </label>
                    </div>
                </form>
            </div>
        </div>

        <!-- Test 2: Survey Style Radio -->
        <div class="card bg-base-100 shadow-lg mb-6">
            <div class="card-body">
                <h2 class="card-title">Test 2: Survey Style Radio</h2>
                <form>
                    <div class="space-y-3">
                        <label class="cursor-pointer">
                            <div class="flex items-start p-4 border-2 border-base-300 rounded-lg hover:border-primary transition-colors">
                                <input type="radio" name="test2" value="1" class="radio radio-primary mt-1 mr-4">
                                <div class="flex-1">
                                    <div class="font-semibold text-base-content">1</div>
                                    <div class="text-sm text-base-content/80 mt-1">Developing - I struggle with this and need significant support</div>
                                </div>
                            </div>
                        </label>
                        <label class="cursor-pointer">
                            <div class="flex items-start p-4 border-2 border-base-300 rounded-lg hover:border-primary transition-colors">
                                <input type="radio" name="test2" value="2" class="radio radio-primary mt-1 mr-4">
                                <div class="flex-1">
                                    <div class="font-semibold text-base-content">2</div>
                                    <div class="text-sm text-base-content/80 mt-1">Progressing - I understand this but apply it inconsistently</div>
                                </div>
                            </div>
                        </label>
                        <label class="cursor-pointer">
                            <div class="flex items-start p-4 border-2 border-base-300 rounded-lg hover:border-primary transition-colors">
                                <input type="radio" name="test2" value="3" class="radio radio-primary mt-1 mr-4">
                                <div class="flex-1">
                                    <div class="font-semibold text-base-content">3</div>
                                    <div class="text-sm text-base-content/80 mt-1">Proficient - I consistently apply this skill with confidence</div>
                                </div>
                            </div>
                        </label>
                        <label class="cursor-pointer">
                            <div class="flex items-start p-4 border-2 border-base-300 rounded-lg hover:border-primary transition-colors">
                                <input type="radio" name="test2" value="4" class="radio radio-primary mt-1 mr-4">
                                <div class="flex-1">
                                    <div class="font-semibold text-base-content">4</div>
                                    <div class="text-sm text-base-content/80 mt-1">Advanced - I master this skill and can guide others</div>
                                </div>
                            </div>
                        </label>
                    </div>
                    <button type="submit" class="btn btn-primary mt-4">Submit Test</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Test JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Radio button test page loaded');
            
            const radioInputs = document.querySelectorAll('input[type="radio"]');
            radioInputs.forEach(radio => {
                radio.addEventListener('change', function() {
                    console.log(`Radio changed: ${this.name} = ${this.value}`);
                    
                    // Update styling for test2
                    if (this.name === 'test2') {
                        const groupName = this.name;
                        const allInGroup = document.querySelectorAll(`input[name="${groupName}"]`);
                        allInGroup.forEach(r => {
                            const label = r.closest('label');
                            if (label) {
                                const div = label.querySelector('div');
                                if (div) {
                                    div.classList.remove('border-primary', 'bg-primary/10');
                                    div.classList.add('border-base-300');
                                }
                            }
                        });
                        
                        const selectedLabel = this.closest('label');
                        if (selectedLabel) {
                            const div = selectedLabel.querySelector('div');
                            if (div) {
                                div.classList.remove('border-base-300');
                                div.classList.add('border-primary', 'bg-primary/10');
                            }
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>
