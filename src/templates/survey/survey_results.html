{% extends "base.html" %}
{% load static %}

{% block title %}Survey Results - {{ survey.title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-base-content mb-2">Survey Results</h1>
            <p class="text-base-content/70">{{ survey.title }}</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <a href="{% url 'survey:survey_detail' slug=survey.slug %}" class="btn btn-outline">
                <i class="fas fa-arrow-left mr-2"></i>Back to Survey
            </a>
            <button onclick="window.print()" class="btn btn-ghost">
                <i class="fas fa-print mr-2"></i>Print Results
            </button>
        </div>
    </div>

    <!-- Overall Score Card -->
    <div class="card bg-gradient-to-r from-primary to-secondary text-primary-content shadow-xl mb-8">
        <div class="card-body text-center">
            <h2 class="card-title text-2xl justify-center mb-4">
                <i class="fas fa-trophy mr-2"></i>Overall Performance
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Score -->
                <div>
                    <div class="text-4xl font-bold mb-2">{{ score_percentage|floatformat:1 }}%</div>
                    <div class="text-lg opacity-90">Final Score</div>
                    <div class="text-sm opacity-75">{{ total_score|floatformat:1 }} / {{ max_score|floatformat:1 }} points</div>
                </div>
                
                <!-- Performance Level -->
                <div>
                    {% if ai_analysis.performance_level %}
                    <div class="text-2xl font-bold mb-2">{{ ai_analysis.performance_level }}</div>
                    <div class="text-lg opacity-90">Performance Level</div>
                    {% else %}
                    <div class="text-2xl font-bold mb-2">Completed</div>
                    <div class="text-lg opacity-90">Status</div>
                    {% endif %}
                </div>
                
                <!-- Completion Time -->
                <div>
                    {% if completion_time %}
                    <div class="text-2xl font-bold mb-2">{{ completion_time.total_seconds|floatformat:0 }}s</div>
                    <div class="text-lg opacity-90">Completion Time</div>
                    <div class="text-sm opacity-75">{{ completion_time }}</div>
                    {% else %}
                    <div class="text-2xl font-bold mb-2">-</div>
                    <div class="text-lg opacity-90">Time</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Results -->
        <div class="lg:col-span-2 space-y-6">
            
            <!-- AI Analysis Insights -->
            {% if ai_analysis.insights %}
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title text-xl mb-4">
                        <i class="fas fa-robot text-info mr-2"></i>AI Analysis & Insights
                    </h3>
                    
                    <div class="space-y-3">
                        {% for insight in ai_analysis.insights %}
                        <div class="flex items-start">
                            <i class="fas fa-lightbulb text-warning mr-3 mt-1"></i>
                            <p class="text-base-content/80">{{ insight }}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Strengths and Improvements -->
            {% if ai_analysis.strengths or ai_analysis.areas_for_improvement %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Strengths -->
                {% if ai_analysis.strengths %}
                <div class="card bg-success/10 border border-success/20">
                    <div class="card-body">
                        <h4 class="card-title text-success">
                            <i class="fas fa-check-circle mr-2"></i>Strengths
                        </h4>
                        <ul class="space-y-2">
                            {% for strength in ai_analysis.strengths %}
                            <li class="flex items-start">
                                <i class="fas fa-plus text-success mr-2 mt-1 text-sm"></i>
                                <span class="text-sm">{{ strength }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}

                <!-- Areas for Improvement -->
                {% if ai_analysis.areas_for_improvement %}
                <div class="card bg-warning/10 border border-warning/20">
                    <div class="card-body">
                        <h4 class="card-title text-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Areas for Improvement
                        </h4>
                        <ul class="space-y-2">
                            {% for area in ai_analysis.areas_for_improvement %}
                            <li class="flex items-start">
                                <i class="fas fa-arrow-up text-warning mr-2 mt-1 text-sm"></i>
                                <span class="text-sm">{{ area }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Recommendations -->
            {% if ai_analysis.recommendations %}
            <div class="card bg-info/10 border border-info/20">
                <div class="card-body">
                    <h3 class="card-title text-info mb-4">
                        <i class="fas fa-compass mr-2"></i>Recommendations for Improvement
                    </h3>
                    
                    <div class="grid grid-cols-1 gap-3">
                        {% for recommendation in ai_analysis.recommendations %}
                        <div class="flex items-start p-3 bg-base-100 rounded-lg">
                            <div class="badge badge-info badge-sm mr-3 mt-1">{{ forloop.counter }}</div>
                            <p class="text-sm">{{ recommendation }}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Detailed Question Responses -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title mb-4">
                        <i class="fas fa-list-alt mr-2"></i>Detailed Responses
                    </h3>
                    
                    <div class="space-y-4">
                        {% for question_id, response_data in responses.items %}
                        <div class="border border-base-300 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-semibold text-base-content">{{ response_data.question.question_text }}</h4>
                                <div class="badge badge-outline">{{ response_data.question.get_question_type_display }}</div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <span class="text-sm text-base-content/70">Your Response:</span>
                                    <p class="font-medium">{{ response_data.display_value }}</p>
                                </div>
                                
                                {% if response_data.score is not None %}
                                <div>
                                    <span class="text-sm text-base-content/70">Score:</span>
                                    <p class="font-medium">
                                        {{ response_data.score|floatformat:1 }} / {{ response_data.response.get_max_score }}
                                        <span class="text-sm text-base-content/70">
                                            ({{ response_data.score|div:response_data.response.get_max_score|mul:100|floatformat:0 }}%)
                                        </span>
                                    </p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-6">
            
            <!-- Score Breakdown -->
            {% if score_breakdown %}
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title mb-4">
                        <i class="fas fa-chart-pie mr-2"></i>Score Breakdown
                    </h3>
                    
                    {% for category, breakdown in score_breakdown.items %}
                    <div class="mb-4">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium">{{ category }}</span>
                            <span class="text-sm">{{ breakdown.score|floatformat:1 }}/{{ breakdown.max_score|floatformat:1 }}</span>
                        </div>
                        <progress class="progress progress-primary w-full" 
                                 value="{{ breakdown.score }}" 
                                 max="{{ breakdown.max_score }}"></progress>
                        <div class="text-xs text-base-content/70 mt-1">
                            {{ breakdown.count }} question{{ breakdown.count|pluralize }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Performance Metrics -->
            {% if ai_analysis.performance_metrics %}
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title mb-4">
                        <i class="fas fa-chart-line mr-2"></i>Performance Metrics
                    </h3>
                    
                    <div class="space-y-3">
                        {% if ai_analysis.performance_metrics.overall_percentage %}
                        <div class="stat">
                            <div class="stat-title text-xs">Overall Score</div>
                            <div class="stat-value text-lg">{{ ai_analysis.performance_metrics.overall_percentage|floatformat:1 }}%</div>
                        </div>
                        {% endif %}
                        
                        {% if ai_analysis.performance_metrics.completion_rate %}
                        <div class="stat">
                            <div class="stat-title text-xs">Completion Rate</div>
                            <div class="stat-value text-lg">{{ ai_analysis.performance_metrics.completion_rate|floatformat:1 }}%</div>
                        </div>
                        {% endif %}
                        
                        {% if ai_analysis.performance_metrics.average_response_quality %}
                        <div class="stat">
                            <div class="stat-title text-xs">Response Quality</div>
                            <div class="stat-value text-lg">{{ ai_analysis.performance_metrics.average_response_quality|floatformat:1 }}%</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Survey Information -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title mb-4">
                        <i class="fas fa-info-circle mr-2"></i>Survey Information
                    </h3>
                    
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="font-semibold">Survey Type:</span>
                            <div class="badge badge-primary badge-sm ml-2">{{ survey.get_survey_type_display }}</div>
                        </div>
                        
                        <div>
                            <span class="font-semibold">Completed:</span>
                            <span class="ml-2">{{ session.completed_at|date:"M d, Y H:i" }}</span>
                        </div>
                        
                        <div>
                            <span class="font-semibold">Questions:</span>
                            <span class="ml-2">{{ responses|length }} answered</span>
                        </div>
                        
                        {% if ai_analysis.analysis_timestamp %}
                        <div>
                            <span class="font-semibold">AI Analysis:</span>
                            <span class="ml-2">{{ ai_analysis.analysis_timestamp|date:"M d, Y H:i" }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title mb-4">
                        <i class="fas fa-cog mr-2"></i>Actions
                    </h3>
                    
                    <div class="space-y-3">
                        {% if survey.multiple_responses %}
                        <a href="{% url 'survey:start_survey' slug=survey.slug %}" 
                           class="btn btn-primary btn-sm w-full">
                            <i class="fas fa-redo mr-2"></i>Retake Survey
                        </a>
                        {% endif %}
                        
                        <button onclick="window.print()" class="btn btn-outline btn-sm w-full">
                            <i class="fas fa-print mr-2"></i>Print Results
                        </button>
                        
                        <a href="{% url 'survey:survey_list' %}" class="btn btn-ghost btn-sm w-full">
                            <i class="fas fa-list mr-2"></i>Browse Surveys
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn, .navbar, .footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
    }
}
</style>
{% endblock %}
