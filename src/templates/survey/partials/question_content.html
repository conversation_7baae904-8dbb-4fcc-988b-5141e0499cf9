<!-- Question Card -->
<div class="card bg-base-100 shadow-lg">
    <div class="card-body">
        <!-- Question Header -->
        <div class="flex items-start justify-between mb-6">
            <div class="flex-1">
                <h2 class="text-xl font-semibold text-base-content mb-2">
                    {{ question.question_text }}
                </h2>
                {% if question.help_text %}
                <p class="text-base-content/70 text-sm">{{ question.help_text }}</p>
                {% endif %}
                {% if question.is_required %}
                <span class="text-error text-sm">* Required</span>
                {% endif %}
            </div>
            <div class="badge badge-outline">{{ question.get_question_type_display }}</div>
        </div>

        <!-- DEBUG INFO -->
        <div class="alert alert-info mb-4">
            <div class="text-xs">
                <strong>DEBUG:</strong><br>
                Question Type: {{ question_type }}<br>
                Scale Options Count: {{ scale_options|length|default:"0" }}<br>
                Selected Value: {{ selected_value|default:"None" }}<br>
                {% if scale_options %}
                Scale Options:<br>
                {% for opt in scale_options %}
                - Value: {{ opt.value }}, Label: {{ opt.label }}<br>
                {% endfor %}
                {% else %}
                NO SCALE OPTIONS FOUND!<br>
                {% endif %}
            </div>
        </div>

        <!-- Answer Form -->
        <form id="question-form"
              method="post"
              hx-post="{% url 'survey:take_survey' session_id=session.id %}"
              hx-target="#question-container"
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator">
            {% csrf_token %}
            
            <!-- Likert Scale Questions (5-point) -->
            {% if question.question_type == 'LIKERT_5' %}
            <div class="form-control mb-6">
                <div class="space-y-3">
                    {% for option in scale_options %}
                    <label class="cursor-pointer">
                        <div class="flex items-center p-3 border-2 border-base-300 rounded-lg hover:border-primary transition-colors">
                            <input type="radio"
                                   name="question_{{ question.id }}"
                                   value="{{ option.value }}"
                                   class="radio radio-primary mr-3"
                                   {% if selected_value == option.value %}checked{% endif %}
                                   {% if question.is_required %}required{% endif %}>
                            <div class="flex-1">{{ option.label }}</div>
                        </div>
                    </label>
                    {% endfor %}
                </div>
                <div class="flex justify-between text-xs text-base-content/60 mt-2">
                    <span>Strongly Disagree</span>
                    <span>Neutral</span>
                    <span>Strongly Agree</span>
                </div>
            </div>

            <!-- Likert Scale Questions (7-point) -->
            {% elif question.question_type == 'LIKERT_7' %}
            <div class="form-control mb-6">
                <div class="radio-group">
                    {% for option in scale_options %}
                    <label class="radio-option">
                        <input type="radio" 
                               name="question_{{ question.id }}" 
                               value="{{ option.value }}"
                               class="radio radio-primary mr-3"
                               {% if selected_value == option.value %}checked{% endif %}
                               {% if question.is_required %}required{% endif %}>
                        <div class="radio-content flex-1">{{ option.label }}</div>
                    </label>
                    {% endfor %}
                </div>
                <div class="scale-labels">
                    <span>Strongly Disagree</span>
                    <span>Neutral</span>
                    <span>Strongly Agree</span>
                </div>
            </div>

            <!-- Rating Scale (1-10) -->
            {% elif question.question_type == 'RATING_10' %}
            <div class="form-control mb-6">
                <div class="grid grid-cols-5 md:grid-cols-10 gap-2">
                    {% for option in scale_options %}
                    <label class="flex flex-col items-center p-2 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors">
                        <input type="radio" 
                               name="question_{{ question.id }}" 
                               value="{{ option.value }}"
                               class="radio radio-primary mb-1"
                               {% if selected_value == option.value %}checked{% endif %}
                               {% if question.is_required %}required{% endif %}>
                        <span class="text-sm font-medium">{{ option.value }}</span>
                    </label>
                    {% endfor %}
                </div>
                <div class="scale-labels mt-2">
                    <span>Poor</span>
                    <span>Excellent</span>
                </div>
            </div>

            <!-- Custom Scale -->
            {% elif question.question_type == 'SCALE_CUSTOM' %}
            <div class="form-control w-full">
                <p class="text-red-500 mb-2">DEBUG: About to render {{ scale_options|length }} radio options</p>
                {% for option in scale_options %}
                <div class="form-control">
                    <label class="label cursor-pointer">
                        <span class="label-text">
                            <strong>{{ option.value }}</strong> - {{ option.label }}
                        </span>
                        <input type="radio"
                               name="question_{{ question.id }}"
                               value="{{ option.value }}"
                               class="radio checked:bg-blue-500"
                               {% if selected_value == option.value %}checked{% endif %}
                               {% if question.is_required %}required{% endif %}>
                    </label>
                </div>
                <p class="text-green-500 text-xs">DEBUG: Rendered option {{ forloop.counter }}: {{ option.value }}</p>
                {% empty %}
                <p class="text-red-500">DEBUG: NO OPTIONS IN LOOP!</p>
                {% endfor %}
                <p class="text-blue-500 mt-2">DEBUG: Finished rendering radio options</p>
            </div>

            <!-- Multiple Choice -->
            {% elif question.question_type == 'MULTIPLE_CHOICE' %}
            <div class="form-control mb-6">
                <div class="space-y-3">
                    {% for choice in choices %}
                    <label class="cursor-pointer">
                        <div class="flex items-center p-3 border-2 border-base-300 rounded-lg hover:border-primary transition-colors">
                            <input type="radio"
                                   name="question_{{ question.id }}"
                                   value="{{ choice.id }}"
                                   class="radio radio-primary mr-3"
                                   {% if selected_choice == choice.id %}checked{% endif %}
                                   {% if question.is_required %}required{% endif %}>
                            <div class="flex-1">{{ choice.text }}</div>
                        </div>
                    </label>
                    {% endfor %}
                </div>
            </div>

            <!-- Yes/No -->
            {% elif question.question_type == 'YES_NO' %}
            <div class="form-control mb-6">
                <div class="space-y-3">
                    {% for option in yes_no_options %}
                    <label class="cursor-pointer">
                        <div class="flex items-center p-3 border-2 border-base-300 rounded-lg hover:border-primary transition-colors">
                            <input type="radio"
                                   name="question_{{ question.id }}"
                                   value="{{ option.value }}"
                                   class="radio radio-primary mr-3"
                                   {% if selected_value == option.value %}checked{% endif %}
                                   {% if question.is_required %}required{% endif %}>
                            <div class="flex-1">{{ option.label }}</div>
                        </div>
                    </label>
                    {% endfor %}
                </div>
            </div>

            <!-- Short Text -->
            {% elif question.question_type == 'TEXT_SHORT' %}
            <div class="form-control mb-6">
                <input type="text" 
                       name="question_{{ question.id }}" 
                       value="{{ text_value|default:'' }}"
                       placeholder="Enter your response..."
                       class="input input-bordered w-full"
                       maxlength="500"
                       {% if question.is_required %}required{% endif %}>
                <label class="label">
                    <span class="label-text-alt">Maximum 500 characters</span>
                </label>
            </div>

            <!-- Long Text -->
            {% elif question.question_type == 'TEXT_LONG' %}
            <div class="form-control mb-6">
                <textarea name="question_{{ question.id }}" 
                          placeholder="Enter your detailed response..."
                          class="textarea textarea-bordered w-full h-32"
                          maxlength="5000"
                          {% if question.is_required %}required{% endif %}>{{ text_value|default:'' }}</textarea>
                <label class="label">
                    <span class="label-text-alt">Maximum 5000 characters</span>
                </label>
            </div>
            {% endif %}

            <!-- Navigation Buttons -->
            <div class="flex justify-between items-center pt-6 border-t border-base-300">
                <!-- Previous Button (if not first question) -->
                <div>
                    {% if progress.current > 1 %}
                    <button type="button" class="btn btn-outline" onclick="history.back()">
                        <i class="fas fa-arrow-left mr-2"></i>Previous
                    </button>
                    {% else %}
                    <div></div>
                    {% endif %}
                </div>

                <!-- Next/Submit Button -->
                <div class="flex items-center gap-3">
                    {% if progress.current < progress.total %}
                    <button type="submit" class="btn btn-primary">
                        Next Question
                        <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                    {% else %}
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check mr-2"></i>Complete Survey
                    </button>
                    {% endif %}
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Question Navigation (Optional) -->
<div class="card bg-base-100 shadow-lg mt-6">
    <div class="card-body">
        <h3 class="text-lg font-semibold mb-3">Question Navigation</h3>
        <div class="flex flex-wrap gap-2">
            {% for i in "x"|ljust:progress.total %}
            <div class="w-8 h-8 rounded flex items-center justify-center text-sm font-medium
                        {% if forloop.counter == progress.current %}
                        bg-primary text-primary-content
                        {% elif forloop.counter < progress.current %}
                        bg-success text-success-content
                        {% else %}
                        bg-base-300 text-base-content
                        {% endif %}">
                {{ forloop.counter }}
            </div>
            {% endfor %}
        </div>
        <div class="text-xs text-base-content/70 mt-2">
            <span class="inline-block w-3 h-3 bg-success rounded mr-1"></span>Completed
            <span class="inline-block w-3 h-3 bg-primary rounded mr-1 ml-3"></span>Current
            <span class="inline-block w-3 h-3 bg-base-300 rounded mr-1 ml-3"></span>Upcoming
        </div>
    </div>
</div>
