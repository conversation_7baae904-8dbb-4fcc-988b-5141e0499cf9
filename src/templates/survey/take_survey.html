{% extends "base.html" %}
{% load static %}

{% block title %}{{ survey.title }} - Question {{ progress.current }} of {{ progress.total }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Progress Header -->
    <div class="card bg-base-100 shadow-lg mb-6">
        <div class="card-body">
            <!-- Survey Title -->
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                <h1 class="text-2xl font-bold text-base-content">{{ survey.title }}</h1>
                <div class="badge badge-primary badge-lg mt-2 md:mt-0">
                    Question {{ progress.current }} of {{ progress.total }}
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="w-full">
                <div class="flex justify-between text-sm text-base-content/70 mb-2">
                    <span>Progress</span>
                    <span>{{ progress.percentage|floatformat:0 }}% Complete</span>
                </div>
                <progress class="progress progress-primary w-full" 
                         value="{{ progress.percentage }}" max="100"></progress>
            </div>
        </div>
    </div>

    <!-- Question Container -->
    <div id="question-container">
        {% include "survey/partials/question_content.html" %}
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-base-100 rounded-lg p-6 flex items-center">
                <span class="loading loading-spinner loading-md mr-3"></span>
                <span>Processing your response...</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-save functionality for text inputs
let autoSaveTimeout;

function autoSave(element) {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
        // Could implement auto-save here if needed
        console.log('Auto-save triggered');
    }, 2000);
}

// Add event listeners for inputs
document.addEventListener('DOMContentLoaded', function() {
    const textInputs = document.querySelectorAll('input[type="text"], textarea');
    textInputs.forEach(input => {
        input.addEventListener('input', () => autoSave(input));
    });

    // Ensure radio buttons work properly
    const radioInputs = document.querySelectorAll('input[type="radio"]');
    radioInputs.forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove checked styling from all options in this group
            const groupName = this.name;
            const allInGroup = document.querySelectorAll(`input[name="${groupName}"]`);
            allInGroup.forEach(r => {
                const label = r.closest('label');
                if (label) {
                    const div = label.querySelector('div');
                    if (div) {
                        div.classList.remove('border-primary', 'bg-primary/10');
                        div.classList.add('border-base-300');
                    }
                }
            });

            // Add checked styling to selected option
            const selectedLabel = this.closest('label');
            if (selectedLabel) {
                const div = selectedLabel.querySelector('div');
                if (div) {
                    div.classList.remove('border-base-300');
                    div.classList.add('border-primary', 'bg-primary/10');
                }
            }
        });

        // Set initial state for pre-selected options
        if (radio.checked) {
            radio.dispatchEvent(new Event('change'));
        }
    });
});

// Handle form submission
document.addEventListener('htmx:beforeRequest', function(event) {
    // Validate required fields
    const form = event.target.closest('form');
    if (form) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('input-error');
            } else {
                field.classList.remove('input-error');
            }
        });
        
        if (!isValid) {
            event.preventDefault();
            alert('Please answer all required questions before continuing.');
            return false;
        }
    }
});

// Handle successful response
document.addEventListener('htmx:afterSwap', function(event) {
    if (event.target.id === 'question-container') {
        // Scroll to top of question
        event.target.scrollIntoView({ behavior: 'smooth', block: 'start' });

        // Re-attach event listeners for new content
        const textInputs = event.target.querySelectorAll('input[type="text"], textarea');
        textInputs.forEach(input => {
            input.addEventListener('input', () => autoSave(input));
        });

        // Re-initialize radio buttons
        const radioInputs = event.target.querySelectorAll('input[type="radio"]');
        radioInputs.forEach(radio => {
            radio.addEventListener('change', function() {
                const groupName = this.name;
                const allInGroup = document.querySelectorAll(`input[name="${groupName}"]`);
                allInGroup.forEach(r => {
                    const label = r.closest('label');
                    if (label) {
                        const div = label.querySelector('div');
                        if (div) {
                            div.classList.remove('border-primary', 'bg-primary/10');
                            div.classList.add('border-base-300');
                        }
                    }
                });

                const selectedLabel = this.closest('label');
                if (selectedLabel) {
                    const div = selectedLabel.querySelector('div');
                    if (div) {
                        div.classList.remove('border-base-300');
                        div.classList.add('border-primary', 'bg-primary/10');
                    }
                }
            });

            if (radio.checked) {
                radio.dispatchEvent(new Event('change'));
            }
        });
    }
});

// Handle errors
document.addEventListener('htmx:responseError', function(event) {
    alert('An error occurred while saving your response. Please try again.');
});

// Prevent accidental page refresh
window.addEventListener('beforeunload', function(event) {
    event.preventDefault();
    event.returnValue = 'Are you sure you want to leave? Your progress may be lost.';
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: flex;
}

.input-error {
    border-color: #ef4444 !important;
}

/* Enhanced radio button styling */
input[type="radio"]:checked + div,
input[type="radio"]:checked ~ div {
    font-weight: 600;
}

label:has(input[type="radio"]:checked) > div {
    border-color: hsl(var(--p)) !important;
    background-color: hsl(var(--p) / 0.1) !important;
}

/* Ensure radio buttons are clickable */
.radio {
    cursor: pointer;
}

/* Focus states for accessibility */
input[type="radio"]:focus {
    outline: 2px solid hsl(var(--p));
    outline-offset: 2px;
}

/* Hover effects */
label:hover > div {
    border-color: hsl(var(--p) / 0.5) !important;
}
</style>
{% endblock %}
