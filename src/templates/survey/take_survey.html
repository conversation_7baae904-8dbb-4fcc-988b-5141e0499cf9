{% extends "base.html" %}
{% load static %}

{% block title %}{{ survey.title }} - Question {{ progress.current }} of {{ progress.total }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Progress Header -->
    <div class="card bg-base-100 shadow-lg mb-6">
        <div class="card-body">
            <!-- Survey Title -->
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                <h1 class="text-2xl font-bold text-base-content">{{ survey.title }}</h1>
                <div class="badge badge-primary badge-lg mt-2 md:mt-0">
                    Question {{ progress.current }} of {{ progress.total }}
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="w-full">
                <div class="flex justify-between text-sm text-base-content/70 mb-2">
                    <span>Progress</span>
                    <span>{{ progress.percentage|floatformat:0 }}% Complete</span>
                </div>
                <progress class="progress progress-primary w-full" 
                         value="{{ progress.percentage }}" max="100"></progress>
            </div>
        </div>
    </div>

    <!-- Question Container -->
    <div id="question-container">
        {% include "survey/partials/question_content.html" %}
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-base-100 rounded-lg p-6 flex items-center">
                <span class="loading loading-spinner loading-md mr-3"></span>
                <span>Processing your response...</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
console.log('Survey page loaded - checking radio buttons');

// Simple form validation for HTMX
document.addEventListener('htmx:beforeRequest', function(event) {
    const form = event.target.closest('form');
    if (form) {
        const requiredRadios = form.querySelectorAll('input[type="radio"][required]');
        let isValid = true;

        // Check radio button groups
        const radioGroups = new Set();
        requiredRadios.forEach(radio => radioGroups.add(radio.name));

        radioGroups.forEach(groupName => {
            const groupRadios = form.querySelectorAll(`input[name="${groupName}"]`);
            const isChecked = Array.from(groupRadios).some(radio => radio.checked);
            if (!isChecked) {
                isValid = false;
            }
        });

        if (!isValid) {
            event.preventDefault();
            alert('Please answer all required questions before continuing.');
            return false;
        }
    }
});

// Handle successful response
document.addEventListener('htmx:afterSwap', function(event) {
    if (event.target.id === 'question-container') {
        event.target.scrollIntoView({ behavior: 'smooth', block: 'start' });
        console.log('New question loaded');
    }
});
                            div.classList.add('border-base-300');
                        }
                    }
                });

                const selectedLabel = this.closest('label');
                if (selectedLabel) {
                    const div = selectedLabel.querySelector('div');
                    if (div) {
                        div.classList.remove('border-base-300');
                        div.classList.add('border-primary', 'bg-primary/10');
                    }
                }
            });

            if (radio.checked) {
                radio.dispatchEvent(new Event('change'));
            }
        });
    }
});

// Handle errors
document.addEventListener('htmx:responseError', function(event) {
    alert('An error occurred while saving your response. Please try again.');
});

// Prevent accidental page refresh
window.addEventListener('beforeunload', function(event) {
    event.preventDefault();
    event.returnValue = 'Are you sure you want to leave? Your progress may be lost.';
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: flex;
}

.input-error {
    border-color: #ef4444 !important;
}

/* Enhanced radio button styling */
input[type="radio"]:checked + div,
input[type="radio"]:checked ~ div {
    font-weight: 600;
}

label:has(input[type="radio"]:checked) > div {
    border-color: hsl(var(--p)) !important;
    background-color: hsl(var(--p) / 0.1) !important;
}

/* Ensure radio buttons are clickable */
.radio {
    cursor: pointer;
}

/* Focus states for accessibility */
input[type="radio"]:focus {
    outline: 2px solid hsl(var(--p));
    outline-offset: 2px;
}

/* Hover effects */
label:hover > div {
    border-color: hsl(var(--p) / 0.5) !important;
}
</style>
{% endblock %}
