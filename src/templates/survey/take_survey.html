{% extends "base.html" %}
{% load static %}

{% block title %}{{ survey.title }} - Question {{ progress.current }} of {{ progress.total }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Progress Header -->
    <div class="card bg-base-100 shadow-lg mb-6">
        <div class="card-body">
            <!-- Survey Title -->
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                <h1 class="text-2xl font-bold text-base-content">{{ survey.title }}</h1>
                <div class="badge badge-primary badge-lg mt-2 md:mt-0">
                    Question {{ progress.current }} of {{ progress.total }}
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="w-full">
                <div class="flex justify-between text-sm text-base-content/70 mb-2">
                    <span>Progress</span>
                    <span>{{ progress.percentage|floatformat:0 }}% Complete</span>
                </div>
                <progress class="progress progress-primary w-full" 
                         value="{{ progress.percentage }}" max="100"></progress>
            </div>
        </div>
    </div>

    <!-- Question Container -->
    <div id="question-container">
        {% include "survey/partials/question_content.html" %}
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-base-100 rounded-lg p-6 flex items-center">
                <span class="loading loading-spinner loading-md mr-3"></span>
                <span>Processing your response...</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-save functionality for text inputs
let autoSaveTimeout;

function autoSave(element) {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
        // Could implement auto-save here if needed
        console.log('Auto-save triggered');
    }, 2000);
}

// Add event listeners for text inputs
document.addEventListener('DOMContentLoaded', function() {
    const textInputs = document.querySelectorAll('input[type="text"], textarea');
    textInputs.forEach(input => {
        input.addEventListener('input', () => autoSave(input));
    });
});

// Handle form submission
document.addEventListener('htmx:beforeRequest', function(event) {
    // Validate required fields
    const form = event.target.closest('form');
    if (form) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('input-error');
            } else {
                field.classList.remove('input-error');
            }
        });
        
        if (!isValid) {
            event.preventDefault();
            alert('Please answer all required questions before continuing.');
            return false;
        }
    }
});

// Handle successful response
document.addEventListener('htmx:afterSwap', function(event) {
    if (event.target.id === 'question-container') {
        // Scroll to top of question
        event.target.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // Re-attach event listeners for new content
        const textInputs = event.target.querySelectorAll('input[type="text"], textarea');
        textInputs.forEach(input => {
            input.addEventListener('input', () => autoSave(input));
        });
    }
});

// Handle errors
document.addEventListener('htmx:responseError', function(event) {
    alert('An error occurred while saving your response. Please try again.');
});

// Prevent accidental page refresh
window.addEventListener('beforeunload', function(event) {
    event.preventDefault();
    event.returnValue = 'Are you sure you want to leave? Your progress may be lost.';
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: flex;
}

.input-error {
    border-color: #ef4444 !important;
}

/* Custom radio button styling for better UX */
.radio-group {
    display: grid;
    gap: 0.75rem;
}

.radio-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 2px solid transparent;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
}

.radio-option:hover {
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
    background: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));
}

.radio-option input[type="radio"]:checked + .radio-content {
    font-weight: 600;
}

.radio-option:has(input[type="radio"]:checked) {
    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));
    background: var(--fallback-p,oklch(var(--p)/0.1));
}

.scale-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--fallback-bc,oklch(var(--bc)/0.6));
}
</style>
{% endblock %}
