{% extends "base.html" %}
{% load static %}

{% block title %}{{ survey.title }} - Question {{ progress.current }} of {{ progress.total }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Progress Header -->
    <div class="card bg-base-100 shadow-lg mb-6">
        <div class="card-body">
            <!-- Survey Title -->
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                <h1 class="text-2xl font-bold text-base-content">{{ survey.title }}</h1>
                <div class="badge badge-primary badge-lg mt-2 md:mt-0">
                    Question {{ progress.current }} of {{ progress.total }}
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="w-full">
                <div class="flex justify-between text-sm text-base-content/70 mb-2">
                    <span>Progress</span>
                    <span>{{ progress.percentage|floatformat:0 }}% Complete</span>
                </div>
                <progress class="progress progress-primary w-full" 
                         value="{{ progress.percentage }}" max="100"></progress>
            </div>
        </div>
    </div>

    <!-- Question Container -->
    <div id="question-container">
        {% include "survey/partials/question_content.html" %}
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-base-100 rounded-lg p-6 flex items-center">
                <span class="loading loading-spinner loading-md mr-3"></span>
                <span>Processing your response...</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Pure DaisyUI - no custom JavaScript needed -->
{% endblock %}

{% block extra_css %}
<style>
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: flex;
}
</style>
{% endblock %}
