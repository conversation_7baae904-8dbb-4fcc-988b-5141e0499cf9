{% extends "base.html" %}
{% load static %}

{% block title %}Surveys - QuizLog{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-base-content mb-2">Available Surveys</h1>
            <p class="text-base-content/70">Participate in communication benchmarks and assessments</p>
        </div>
        
        {% if user.is_authenticated %}
        <div class="mt-4 md:mt-0">
            <a href="{% url 'survey:survey_create' %}" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i>Create Survey
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Filters -->
    <div class="card bg-base-100 shadow-lg mb-8">
        <div class="card-body">
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Search</span>
                    </label>
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="Search surveys..." class="input input-bordered">
                </div>

                <!-- Category Filter -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Category</span>
                    </label>
                    <select name="category" class="select select-bordered">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.slug }}" {% if current_category == category.slug %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Type Filter -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Type</span>
                    </label>
                    <select name="type" class="select select-bordered">
                        <option value="">All Types</option>
                        {% for type_code, type_name in survey_types %}
                        <option value="{{ type_code }}" {% if current_type == type_code %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Submit -->
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">&nbsp;</span>
                    </label>
                    <button type="submit" class="btn btn-outline">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Survey Grid -->
    {% if surveys %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {% for survey in surveys %}
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
            <div class="card-body">
                <!-- Survey Type Badge -->
                <div class="flex justify-between items-start mb-3">
                    <div class="badge badge-primary">{{ survey.get_survey_type_display }}</div>
                    {% if survey.enable_ai_analysis %}
                    <div class="badge badge-secondary">AI Analysis</div>
                    {% endif %}
                </div>

                <!-- Title and Description -->
                <h2 class="card-title text-lg mb-2">{{ survey.title }}</h2>
                <p class="text-base-content/70 text-sm mb-4 line-clamp-3">{{ survey.description }}</p>

                <!-- Survey Info -->
                <div class="flex flex-wrap gap-2 mb-4 text-xs">
                    <div class="badge badge-outline">
                        <i class="fas fa-questions mr-1"></i>{{ survey.total_questions }} questions
                    </div>
                    <div class="badge badge-outline">
                        <i class="fas fa-users mr-1"></i>{{ survey.total_responses }} responses
                    </div>
                    {% if survey.enable_scoring %}
                    <div class="badge badge-outline">
                        <i class="fas fa-chart-line mr-1"></i>Scored
                    </div>
                    {% endif %}
                </div>

                <!-- Category and Creator -->
                <div class="text-xs text-base-content/60 mb-4">
                    <div>Category: {{ survey.category.name }}</div>
                    <div>Created by: {{ survey.created_by.username }}</div>
                </div>

                <!-- Action Button -->
                <div class="card-actions justify-end">
                    <a href="{% url 'survey:survey_detail' slug=survey.slug %}" 
                       class="btn btn-primary btn-sm">
                        View Details
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="flex justify-center">
        <div class="btn-group">
            {% if page_obj.has_previous %}
            <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}" 
               class="btn btn-outline">First</a>
            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}" 
               class="btn btn-outline">Previous</a>
            {% endif %}
            
            <span class="btn btn-active">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
            
            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}" 
               class="btn btn-outline">Next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}" 
               class="btn btn-outline">Last</a>
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% else %}
    <!-- No Surveys -->
    <div class="text-center py-12">
        <div class="text-6xl text-base-content/20 mb-4">
            <i class="fas fa-clipboard-list"></i>
        </div>
        <h3 class="text-xl font-semibold text-base-content mb-2">No surveys found</h3>
        <p class="text-base-content/70 mb-6">
            {% if search_query or current_category or current_type %}
            Try adjusting your filters to see more results.
            {% else %}
            There are no surveys available at the moment.
            {% endif %}
        </p>
        {% if user.is_authenticated %}
        <a href="{% url 'survey:survey_create' %}" class="btn btn-primary">
            <i class="fas fa-plus mr-2"></i>Create the First Survey
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
{% endblock %}
