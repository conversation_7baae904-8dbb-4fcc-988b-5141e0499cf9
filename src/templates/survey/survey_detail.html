{% extends "base.html" %}
{% load static %}

{% block title %}{{ survey.title }} - QuizLog{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{% url 'survey:survey_list' %}" class="btn btn-ghost">
            <i class="fas fa-arrow-left mr-2"></i>Back to Surveys
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Survey Header -->
            <div class="card bg-base-100 shadow-lg mb-6">
                <div class="card-body">
                    <!-- Type Badge -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <div class="badge badge-primary badge-lg">{{ survey.get_survey_type_display }}</div>
                        {% if survey.enable_scoring %}
                        <div class="badge badge-secondary">Scored Assessment</div>
                        {% endif %}
                        {% if survey.enable_ai_analysis %}
                        <div class="badge badge-accent">AI Analysis</div>
                        {% endif %}
                    </div>

                    <!-- Title and Description -->
                    <h1 class="text-3xl font-bold text-base-content mb-4">{{ survey.title }}</h1>
                    <p class="text-base-content/80 text-lg leading-relaxed mb-6">{{ survey.description }}</p>

                    <!-- Survey Info Grid -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="stat bg-base-200 rounded-lg p-4">
                            <div class="stat-figure text-primary">
                                <i class="fas fa-question-circle text-2xl"></i>
                            </div>
                            <div class="stat-title text-xs">Questions</div>
                            <div class="stat-value text-lg">{{ total_questions }}</div>
                        </div>

                        <div class="stat bg-base-200 rounded-lg p-4">
                            <div class="stat-figure text-secondary">
                                <i class="fas fa-clock text-2xl"></i>
                            </div>
                            <div class="stat-title text-xs">Est. Time</div>
                            <div class="stat-value text-lg">{{ estimated_time|floatformat:0 }}m</div>
                        </div>

                        <div class="stat bg-base-200 rounded-lg p-4">
                            <div class="stat-figure text-accent">
                                <i class="fas fa-users text-2xl"></i>
                            </div>
                            <div class="stat-title text-xs">Responses</div>
                            <div class="stat-value text-lg">{{ survey.total_responses }}</div>
                        </div>

                        <div class="stat bg-base-200 rounded-lg p-4">
                            <div class="stat-figure text-info">
                                <i class="fas fa-chart-line text-2xl"></i>
                            </div>
                            <div class="stat-title text-xs">Avg Score</div>
                            <div class="stat-value text-lg">{{ survey.average_score|floatformat:0 }}%</div>
                        </div>
                    </div>

                    <!-- Survey Features -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-3">Survey Features</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div class="flex items-center">
                                <i class="fas fa-{% if survey.random_order %}check text-success{% else %}times text-error{% endif %} mr-2"></i>
                                <span>Random Question Order: {% if survey.random_order %}Yes{% else %}No{% endif %}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-{% if survey.allow_anonymous %}check text-success{% else %}times text-error{% endif %} mr-2"></i>
                                <span>Anonymous Responses: {% if survey.allow_anonymous %}Allowed{% else %}Not Allowed{% endif %}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-{% if survey.multiple_responses %}check text-success{% else %}times text-error{% endif %} mr-2"></i>
                                <span>Multiple Attempts: {% if survey.multiple_responses %}Allowed{% else %}One Time Only{% endif %}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-{% if survey.enable_ai_analysis %}robot text-info{% else %}times text-error{% endif %} mr-2"></i>
                                <span>AI Analysis: {% if survey.enable_ai_analysis %}Enabled{% else %}Disabled{% endif %}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Previous Results (if user has completed) -->
                    {% if user_has_completed and user_session %}
                    <div class="alert alert-info mb-6">
                        <div class="flex-1">
                            <i class="fas fa-info-circle mr-2"></i>
                            <span>You completed this survey on {{ user_session.completed_at|date:"M d, Y" }}.</span>
                            <a href="{% url 'survey:survey_results' session_id=user_session.id %}" 
                               class="btn btn-sm btn-outline ml-4">View Results</a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Survey Instructions -->
            {% if survey.survey_type != 'CUSTOM' %}
            <div class="card bg-base-100 shadow-lg mb-6">
                <div class="card-body">
                    <h3 class="text-xl font-semibold mb-4">
                        <i class="fas fa-info-circle mr-2"></i>Instructions
                    </h3>
                    
                    {% if survey.survey_type == 'FACE_TO_FACE' %}
                    <div class="prose max-w-none">
                        <p>This benchmark assesses face-to-face communication skills. Please evaluate the following aspects:</p>
                        <ul>
                            <li><strong>Verbal Communication:</strong> Clarity, volume, pace, and articulation</li>
                            <li><strong>Non-verbal Communication:</strong> Body language, gestures, and facial expressions</li>
                            <li><strong>Eye Contact:</strong> Appropriate frequency and duration</li>
                            <li><strong>Engagement:</strong> Active listening and interaction quality</li>
                            <li><strong>Overall Presence:</strong> Confidence and professional demeanor</li>
                        </ul>
                    </div>
                    {% elif survey.survey_type == 'PHONE' %}
                    <div class="prose max-w-none">
                        <p>This benchmark evaluates phone communication effectiveness. Focus on:</p>
                        <ul>
                            <li><strong>Audio Quality:</strong> Connection clarity and technical issues</li>
                            <li><strong>Vocal Clarity:</strong> Pronunciation, articulation, and speaking pace</li>
                            <li><strong>Phone Etiquette:</strong> Professional greeting, closing, and manners</li>
                            <li><strong>Vocal Variety:</strong> Tone, pitch, and emphasis for engagement</li>
                            <li><strong>Call Structure:</strong> Organization and flow of conversation</li>
                        </ul>
                    </div>
                    {% elif survey.survey_type == 'ZOOM' %}
                    <div class="prose max-w-none">
                        <p>This benchmark assesses video conferencing communication skills. Evaluate:</p>
                        <ul>
                            <li><strong>Technical Setup:</strong> Video and audio quality, lighting</li>
                            <li><strong>Virtual Presence:</strong> Camera positioning and engagement</li>
                            <li><strong>Platform Proficiency:</strong> Use of video conferencing features</li>
                            <li><strong>Virtual Etiquette:</strong> Muting, turn-taking, and digital manners</li>
                            <li><strong>Engagement:</strong> Interaction and participation in virtual environment</li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Start Survey Card -->
            <div class="card bg-primary text-primary-content shadow-lg mb-6">
                <div class="card-body text-center">
                    <h3 class="card-title justify-center mb-4">
                        <i class="fas fa-play-circle mr-2"></i>Ready to Start?
                    </h3>
                    
                    {% if not user.is_authenticated and not survey.allow_anonymous %}
                    <p class="mb-4">You must be logged in to take this survey.</p>
                    <a href="{% url 'account_login' %}" class="btn btn-secondary">
                        <i class="fas fa-sign-in-alt mr-2"></i>Login
                    </a>
                    {% elif user_has_completed and not can_retake %}
                    <p class="mb-4">You have already completed this survey.</p>
                    <a href="{% url 'survey:survey_results' session_id=user_session.id %}" 
                       class="btn btn-secondary">
                        <i class="fas fa-chart-bar mr-2"></i>View Results
                    </a>
                    {% else %}
                    <p class="mb-4">
                        {% if user_has_completed %}
                        Take the survey again to compare your results.
                        {% else %}
                        Begin your assessment now.
                        {% endif %}
                    </p>
                    <a href="{% url 'survey:start_survey' slug=survey.slug %}" 
                       class="btn btn-secondary btn-lg">
                        <i class="fas fa-rocket mr-2"></i>Start Survey
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Survey Details -->
            <div class="card bg-base-100 shadow-lg">
                <div class="card-body">
                    <h3 class="card-title mb-4">Survey Details</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <span class="font-semibold">Category:</span>
                            <div class="badge badge-outline ml-2">{{ survey.category.name }}</div>
                        </div>
                        
                        <div>
                            <span class="font-semibold">Created by:</span>
                            <span class="ml-2">{{ survey.created_by.username }}</span>
                        </div>
                        
                        <div>
                            <span class="font-semibold">Created:</span>
                            <span class="ml-2">{{ survey.created_at|date:"M d, Y" }}</span>
                        </div>
                        
                        <div>
                            <span class="font-semibold">Last updated:</span>
                            <span class="ml-2">{{ survey.updated_at|date:"M d, Y" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
