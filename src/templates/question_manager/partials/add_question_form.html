<form method="post" x-data="questionForm()" hx-post="{% url 'question_manager:add_question_modal' quiz_id=quiz.pk %}" hx-target="#questionFormContainer">
    {% csrf_token %}
    
    <!-- Question Type Selection -->
    <div class="mb-6">
        <label class="block mb-2">
            <span class="my-p font-medium">Question Type *</span>
        </label>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <label class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors"
                   :class="questionType === 'MCQ' ? 'border-primary bg-primary/10' : ''">
                <input type="radio" name="question_type" value="MCQ" class="radio radio-primary" x-model="questionType">
                <div>
                    <div class="my-p font-medium">Multiple Choice</div>
                    <div class="text-xs text-base-content/60">Choose from multiple options</div>
                </div>
            </label>
            
            <label class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors"
                   :class="questionType === 'TF' ? 'border-primary bg-primary/10' : ''">
                <input type="radio" name="question_type" value="TF" class="radio radio-primary" x-model="questionType">
                <div>
                    <div class="my-p font-medium">True/False</div>
                    <div class="text-xs text-base-content/60">Binary choice question</div>
                </div>
            </label>
            
            <label class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors"
                   :class="questionType === 'SUBJECTIVE' ? 'border-primary bg-primary/10' : ''">
                <input type="radio" name="question_type" value="SUBJECTIVE" class="radio radio-primary" x-model="questionType">
                <div>
                    <div class="my-p font-medium">Short Answer</div>
                    <div class="text-xs text-base-content/60">Open-ended response</div>
                </div>
            </label>
        </div>
    </div>

    <!-- Question Content -->
    <div class="mb-6">
        <label class="block mb-2">
            <span class="my-p font-medium">Question *</span>
        </label>
        <textarea name="content" rows="3" class="my-input" 
                  placeholder="Enter your question here..." 
                  required x-model="questionContent"></textarea>
    </div>

    <!-- Question Settings -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
            <label class="block mb-2">
                <span class="my-p font-medium">Points</span>
            </label>
            <input type="number" name="marks" value="1" min="1" max="100" class="my-input" x-model="marks">
        </div>
        
        <div>
            <label class="block mb-2">
                <span class="my-p font-medium">Explanation (Optional)</span>
            </label>
            <input type="text" name="explanation" class="my-input" 
                   placeholder="Explain the correct answer..." x-model="explanation">
        </div>
    </div>

    <!-- Multiple Choice Options -->
    <div x-show="questionType === 'MCQ'" class="mb-6">
        <label class="block mb-2">
            <span class="my-p font-medium">Answer Choices *</span>
        </label>
        <div class="space-y-3">
            <template x-for="(choice, index) in choices" :key="index">
                <div class="flex items-center gap-3">
                    <input type="checkbox" :name="'correct_' + (index + 1)" class="checkbox checkbox-primary"
                           x-model="choice.isCorrect">
                    <input type="text" :name="'choice_' + (index + 1)" class="my-input flex-1" 
                           :placeholder="'Choice ' + (index + 1)" x-model="choice.content"
                           :required="index < 2">
                    <button type="button" class="btn btn-xs btn-ghost" 
                            x-show="index >= 2 && choice.content" 
                            @click="removeChoice(index)">
                        <i class="fa-regular fa-times"></i>
                    </button>
                </div>
            </template>
        </div>
        <button type="button" class="btn btn-sm btn-outline mt-2" 
                x-show="choices.length < 5" @click="addChoice()">
            <i class="fa-regular fa-plus"></i>
            Add Choice
        </button>
        <div class="text-xs text-base-content/60 mt-2">
            Check the box next to correct answers. At least one must be correct.
        </div>
    </div>

    <!-- True/False Options -->
    <div x-show="questionType === 'TF'" class="mb-6">
        <label class="block mb-2">
            <span class="my-p font-medium">Correct Answer *</span>
        </label>
        <div class="flex gap-4">
            <label class="flex items-center gap-2">
                <input type="radio" name="tf_answer" value="true" class="radio radio-primary" checked>
                <span class="my-p">True</span>
            </label>
            <label class="flex items-center gap-2">
                <input type="radio" name="tf_answer" value="false" class="radio radio-primary">
                <span class="my-p">False</span>
            </label>
        </div>
    </div>

    <!-- Short Answer Guidance -->
    <div x-show="questionType === 'SUBJECTIVE'" class="mb-6">
        <div class="bg-info/10 border border-info/20 rounded p-4">
            <div class="flex items-center gap-2 mb-2">
                <i class="fa-regular fa-info-circle text-info"></i>
                <span class="my-p font-medium">Short Answer Question</span>
            </div>
            <p class="text-sm text-base-content/80">
                Short answer questions require manual grading. Students will see a text input field 
                where they can type their response. Use the explanation field to provide grading guidance.
            </p>
        </div>
    </div>

    <!-- Preview -->
    <div x-show="questionContent.length > 0" class="mb-6">
        <label class="block mb-2">
            <span class="my-p font-medium">Preview</span>
        </label>
        <div class="bg-base-200 border border-base-300 rounded p-4">
            <div class="flex items-center gap-2 mb-3">
                <span class="badge badge-primary" x-text="getQuestionTypeLabel()"></span>
                <span class="badge badge-outline" x-text="marks + ' point' + (marks > 1 ? 's' : '')"></span>
            </div>
            
            <div class="my-p font-medium mb-3" x-text="questionContent"></div>
            
            <!-- MCQ Preview -->
            <div x-show="questionType === 'MCQ'" class="space-y-2">
                <template x-for="(choice, index) in choices.filter(c => c.content)" :key="index">
                    <div class="flex items-center gap-2">
                        <div class="w-4 h-4 border border-base-content/40 rounded-full"></div>
                        <span x-text="choice.content" :class="choice.isCorrect ? 'font-medium text-success' : ''"></span>
                        <span x-show="choice.isCorrect" class="text-success text-xs">(Correct)</span>
                    </div>
                </template>
            </div>
            
            <!-- TF Preview -->
            <div x-show="questionType === 'TF'" class="space-y-2">
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 border border-base-content/40 rounded-full"></div>
                    <span>True</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 border border-base-content/40 rounded-full"></div>
                    <span>False</span>
                </div>
            </div>
            
            <!-- SA Preview -->
            <div x-show="questionType === 'SUBJECTIVE'">
                <div class="border border-base-300 rounded p-3 bg-base-100">
                    <span class="text-base-content/60 italic">Student will type their answer here...</span>
                </div>
            </div>
            
            <div x-show="explanation.length > 0" class="mt-3 text-sm text-base-content/80">
                <strong>Explanation:</strong> <span x-text="explanation"></span>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex flex-col sm:flex-row gap-4 justify-end">
        <button type="button" class="btn btn-ghost" onclick="closeAddQuestionModal()">
            Cancel
        </button>
        <button type="submit" class="btn btn-primary" 
                :disabled="!isFormValid()" 
                :class="!isFormValid() ? 'btn-disabled' : ''">
            <i class="fa-regular fa-save"></i>
            Add Question
        </button>
    </div>
</form>

<script>
function questionForm() {
    return {
        questionType: 'MCQ',
        questionContent: '',
        explanation: '',
        marks: 1,
        choices: [
            { content: '', isCorrect: true },
            { content: '', isCorrect: false },
            { content: '', isCorrect: false },
            { content: '', isCorrect: false },
            { content: '', isCorrect: false }
        ],
        
        addChoice() {
            if (this.choices.length < 5) {
                this.choices.push({ content: '', isCorrect: false });
            }
        },
        
        removeChoice(index) {
            if (index >= 2) {
                this.choices.splice(index, 1);
            }
        },
        
        getQuestionTypeLabel() {
            const labels = {
                'MCQ': 'Multiple Choice',
                'TF': 'True/False',
                'SUBJECTIVE': 'Short Answer'
            };
            return labels[this.questionType] || '';
        },
        
        isFormValid() {
            if (!this.questionContent.trim()) return false;
            
            if (this.questionType === 'MCQ') {
                const validChoices = this.choices.filter(c => c.content.trim());
                const hasCorrect = this.choices.some(c => c.isCorrect && c.content.trim());
                return validChoices.length >= 2 && hasCorrect;
            }
            
            return true;
        }
    }
}
</script>

<style>
.my-input {
    @apply w-full border-1 border-neutral-content rounded-sm p-3 outline-none focus:outline-none focus:ring-0 focus:border-primary;
}

textarea.my-input {
    resize: vertical;
}
</style>
