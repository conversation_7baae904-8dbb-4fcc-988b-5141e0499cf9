{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen bg-base-200">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center gap-4 mb-4">
                <a href="{% url 'question_manager:quiz_questions' quiz_id=quiz.pk %}" class="btn btn-ghost btn-sm">
                    <i class="fa-regular fa-arrow-left"></i>
                    Back to Questions
                </a>
                <div>
                    <h1 class="my-h1">Bulk Import Questions</h1>
                    <p class="my-p">Import multiple questions for "{{ quiz.title }}"</p>
                </div>
            </div>
        </div>

        <!-- Quiz Info -->
        <div class="my-card">
            <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <i class="fa-regular fa-clipboard-question text-primary text-xl"></i>
                </div>
                <div class="flex-1">
                    <h2 class="my-h2">{{ quiz.title }}</h2>
                    <p class="my-p text-base-content/60 mb-2">{{ quiz.description|truncatewords:20 }}</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="badge badge-outline">{{ quiz.category.name }}</span>
                        <span class="badge badge-outline">{{ quiz.questions.count }} existing question{{ quiz.questions.count|pluralize }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import Method Selection -->
        <div class="my-card" x-data="bulkImport()">
            <h2 class="my-h2 mb-4">Choose Import Method</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <label class="flex items-start gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors"
                       :class="method === 'text' ? 'border-primary bg-primary/10' : ''">
                    <input type="radio" name="import_method" value="text" class="radio radio-primary mt-1" x-model="method">
                    <div>
                        <div class="my-h3">Text Parsing</div>
                        <div class="my-p text-base-content/60 mb-2">Paste formatted text with questions</div>
                        <div class="text-xs text-base-content/60">
                            <strong>Format:</strong> MC:, TF:, SA: followed by question and answers
                        </div>
                    </div>
                </label>
                
                <label class="flex items-start gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors"
                       :class="method === 'csv' ? 'border-primary bg-primary/10' : ''">
                    <input type="radio" name="import_method" value="csv" class="radio radio-primary mt-1" x-model="method">
                    <div>
                        <div class="my-h3">CSV Upload</div>
                        <div class="my-p text-base-content/60 mb-2">Upload a structured CSV file</div>
                        <div class="text-xs text-base-content/60">
                            <strong>Structured:</strong> Columns for type, question, choices, etc.
                        </div>
                    </div>
                </label>
            </div>

            <!-- Text Import Form -->
            <div x-show="method === 'text'" class="space-y-6">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <input type="hidden" name="method" value="text">
                    
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Question Text *</span>
                        </label>
                        <textarea name="text_content" rows="15" class="my-input" 
                                  placeholder="Paste your questions here using the format below..." 
                                  required x-model="textContent"></textarea>
                        <div class="text-xs text-base-content/60 mt-1">
                            Use the format shown in the example below
                        </div>
                    </div>
                    
                    <div class="flex gap-4 justify-end">
                        <a href="{% url 'question_manager:quiz_questions' quiz_id=quiz.pk %}" class="btn btn-ghost">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" :disabled="!textContent.trim()">
                            <i class="fa-regular fa-upload"></i>
                            Import Questions
                        </button>
                    </div>
                </form>
            </div>

            <!-- CSV Import Form -->
            <div x-show="method === 'csv'" class="space-y-6">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <input type="hidden" name="method" value="csv">
                    
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">CSV File *</span>
                        </label>
                        <input type="file" name="csv_file" accept=".csv" class="file-input file-input-bordered w-full" required>
                        <div class="text-xs text-base-content/60 mt-1">
                            Upload a CSV file with your questions. Download the template below for the correct format.
                        </div>
                    </div>
                    
                    <div class="flex gap-4 justify-between">
                        <a href="{% url 'question_manager:csv_template' %}" class="btn btn-outline">
                            <i class="fa-regular fa-download"></i>
                            Download CSV Template
                        </a>
                        
                        <div class="flex gap-4">
                            <a href="{% url 'question_manager:quiz_questions' quiz_id=quiz.pk %}" class="btn btn-ghost">
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa-regular fa-upload"></i>
                                Import CSV
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Format Examples -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Text Format Example -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Text Format Example</h2>
                <div class="bg-base-200 p-4 rounded border font-mono text-sm">
                    <pre class="whitespace-pre-wrap">MC:
What is the capital of France?
Paris
London
Berlin
Madrid

TF:
Python is a programming language.
TRUE

SA:
Explain the concept of object-oriented programming.

MC:
[Which of these is a web framework?]
Django
Photoshop
Microsoft Word
Calculator</pre>
                </div>
                <div class="mt-4 text-sm text-base-content/80">
                    <h3 class="font-medium mb-2">Format Rules:</h3>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>MC:</strong> Multiple choice - first answer is correct</li>
                        <li><strong>TF:</strong> True/False - specify TRUE or FALSE</li>
                        <li><strong>SA:</strong> Short answer - open-ended question</li>
                        <li>Question can be on same line or in [brackets]</li>
                        <li>Separate questions with blank lines</li>
                    </ul>
                </div>
            </div>

            <!-- CSV Format Example -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">CSV Format Example</h2>
                <div class="overflow-x-auto">
                    <table class="table table-xs table-zebra">
                        <thead>
                            <tr>
                                <th>type</th>
                                <th>question</th>
                                <th>choice_1</th>
                                <th>correct_1</th>
                                <th>choice_2</th>
                                <th>correct_2</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>MCQ</td>
                                <td>Capital of France?</td>
                                <td>Paris</td>
                                <td>true</td>
                                <td>London</td>
                                <td>false</td>
                            </tr>
                            <tr>
                                <td>TF</td>
                                <td>Python is a language?</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>SA</td>
                                <td>Explain OOP</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-sm text-base-content/80">
                    <h3 class="font-medium mb-2">CSV Columns:</h3>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>type:</strong> MCQ, TF, or SA</li>
                        <li><strong>question:</strong> The question text</li>
                        <li><strong>explanation:</strong> Optional explanation</li>
                        <li><strong>marks:</strong> Point value (default: 1)</li>
                        <li><strong>choice_1-5:</strong> Answer choices for MCQ</li>
                        <li><strong>correct_1-5:</strong> true/false for each choice</li>
                        <li><strong>correct_answer:</strong> true/false for TF questions</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>
</div>

<script>
function bulkImport() {
    return {
        method: 'text',
        textContent: '',
        
        init() {
            // Style form inputs
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                if (!input.classList.contains('my-input') && !input.classList.contains('radio') && !input.classList.contains('file-input')) {
                    input.classList.add('my-input');
                }
            });
        }
    }
}
</script>

<style>
.my-input {
    @apply w-full border-1 border-neutral-content rounded-sm p-3 outline-none focus:outline-none focus:ring-0 focus:border-primary;
}

textarea.my-input {
    resize: vertical;
}
</style>
{% endblock %}
