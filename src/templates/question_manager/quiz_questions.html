{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen bg-base-200">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-4">
                    <a href="{% url 'quiz_admin:quiz_detail' pk=quiz.pk %}" class="btn btn-ghost btn-sm">
                        <i class="fa-regular fa-arrow-left"></i>
                        Back to Quiz
                    </a>
                    <div>
                        <h1 class="my-h1">Manage Questions</h1>
                        <p class="my-p">Add and manage questions for "{{ quiz.title }}"</p>
                    </div>
                </div>
                
                <div class="flex gap-2">
                    <a href="{% url 'question_manager:bulk_add_questions' quiz_id=quiz.pk %}" class="btn btn-outline">
                        <i class="fa-regular fa-upload"></i>
                        Bulk Import
                    </a>
                    <button onclick="openAddQuestionModal()" class="btn btn-primary">
                        <i class="fa-regular fa-plus"></i>
                        Add Question
                    </button>
                </div>
            </div>
        </div>

        <!-- Quiz Info -->
        <div class="my-card">
            <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <i class="fa-regular fa-clipboard-question text-primary text-xl"></i>
                </div>
                <div class="flex-1">
                    <h2 class="my-h2">{{ quiz.title }}</h2>
                    <p class="my-p text-base-content/60 mb-2">{{ quiz.description|truncatewords:20 }}</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="badge badge-outline">{{ quiz.category.name }}</span>
                        <span class="badge badge-outline">{{ questions.count }} question{{ questions.count|pluralize }}</span>
                        {% if quiz.time_limit %}
                        <span class="badge badge-outline">{{ quiz.time_limit }} minutes</span>
                        {% endif %}
                        <span class="badge badge-outline">Pass: {{ quiz.pass_mark }}%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <button onclick="openAddQuestionModal()" 
                    class="my-card hover:bg-base-200 transition-colors cursor-pointer">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-plus text-primary text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Add Single Question</div>
                        <div class="my-p text-base-content/60">Create one question at a time</div>
                    </div>
                </div>
            </button>
            
            <a href="{% url 'question_manager:bulk_add_questions' quiz_id=quiz.pk %}" 
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-success/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-upload text-success text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Bulk Import</div>
                        <div class="my-p text-base-content/60">Import via text or CSV</div>
                    </div>
                </div>
            </a>
            
            {% if question_banks %}
            <div class="my-card">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-info/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-database text-info text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Question Banks</div>
                        <div class="my-p text-base-content/60">{{ question_banks.count }} available</div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Questions List -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="my-h2">Questions ({{ questions.count }})</h2>
                <div class="flex gap-2">
                    <button onclick="openAddQuestionModal()" class="btn btn-sm btn-primary">
                        <i class="fa-regular fa-plus"></i>
                        Add Question
                    </button>
                </div>
            </div>

            {% if questions %}
            <div class="space-y-4">
                {% for question in questions %}
                <div class="border border-base-300 rounded-lg p-4 hover:bg-base-200 transition-colors">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <!-- Question Header -->
                            <div class="flex items-center gap-2 mb-2">
                                <span class="badge 
                                           {% if question.question_type == 'MCQ' %}badge-primary
                                           {% elif question.question_type == 'TF' %}badge-success
                                           {% else %}badge-info{% endif %}">
                                    {% if question.question_type == 'MCQ' %}Multiple Choice
                                    {% elif question.question_type == 'TF' %}True/False
                                    {% else %}Short Answer{% endif %}
                                </span>
                                <span class="badge badge-outline">{{ question.marks }} point{{ question.marks|pluralize }}</span>
                            </div>
                            
                            <!-- Question Content -->
                            <div class="my-p font-medium mb-2">{{ question.content }}</div>
                            
                            <!-- Question Choices (for MCQ and TF) -->
                            {% if question.question_type == 'MCQ' or question.question_type == 'TF' %}
                            <div class="ml-4 space-y-1">
                                {% for choice in question.choices.all %}
                                <div class="flex items-center gap-2 text-sm">
                                    {% if choice.is_correct %}
                                    <i class="fa-regular fa-check-circle text-success"></i>
                                    {% else %}
                                    <i class="fa-regular fa-circle text-base-content/40"></i>
                                    {% endif %}
                                    <span class="{% if choice.is_correct %}text-success font-medium{% endif %}">
                                        {{ choice.content }}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}
                            
                            <!-- Explanation -->
                            {% if question.explanation %}
                            <div class="mt-2 text-sm text-base-content/60">
                                <strong>Explanation:</strong> {{ question.explanation }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex gap-1 ml-4">
                            <button class="btn btn-xs btn-outline" title="Edit Question">
                                <i class="fa-regular fa-edit"></i>
                            </button>
                            <button class="btn btn-xs btn-error" title="Delete Question"
                                    onclick="confirmDelete('{{ question.id }}', '{{ question.content|truncatewords:5 }}')">
                                <i class="fa-regular fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-12">
                <i class="fa-regular fa-clipboard-question text-6xl text-base-content/30 mb-4"></i>
                <h3 class="my-h2 mb-2">No Questions Yet</h3>
                <p class="my-p mb-6">Add your first question to get started!</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="openAddQuestionModal()" class="btn btn-primary">
                        <i class="fa-regular fa-plus"></i>
                        Add Single Question
                    </button>
                    <a href="{% url 'question_manager:bulk_add_questions' quiz_id=quiz.pk %}" class="btn btn-outline">
                        <i class="fa-regular fa-upload"></i>
                        Bulk Import Questions
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </main>
</div>

<!-- Add Question Modal -->
<div id="addQuestionModal" class="modal">
    <div class="modal-box w-11/12 max-w-4xl">
        <div class="flex items-center justify-between mb-4">
            <h3 class="my-h2">Add New Question</h3>
            <button class="btn btn-sm btn-circle btn-ghost" onclick="closeAddQuestionModal()">✕</button>
        </div>
        
        <div id="questionFormContainer">
            <!-- Form will be loaded here via HTMX -->
        </div>
    </div>
    <div class="modal-backdrop" onclick="closeAddQuestionModal()"></div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="modal">
    <div class="modal-box">
        <h3 class="my-h2 mb-4">Confirm Delete</h3>
        <p class="my-p mb-6">Are you sure you want to delete this question? This action cannot be undone.</p>
        <div class="flex gap-4 justify-end">
            <button class="btn btn-ghost" onclick="closeDeleteModal()">Cancel</button>
            <button class="btn btn-error" onclick="deleteQuestion()">Delete Question</button>
        </div>
    </div>
    <div class="modal-backdrop" onclick="closeDeleteModal()"></div>
</div>

<script>
let questionToDelete = null;

function openAddQuestionModal() {
    // Load form via HTMX
    htmx.ajax('GET', '{% url "question_manager:add_question_modal" quiz_id=quiz.pk %}', {
        target: '#questionFormContainer',
        swap: 'innerHTML'
    });
    
    document.getElementById('addQuestionModal').classList.add('modal-open');
}

function closeAddQuestionModal() {
    document.getElementById('addQuestionModal').classList.remove('modal-open');
    document.getElementById('questionFormContainer').innerHTML = '';
}

function confirmDelete(questionId, questionText) {
    questionToDelete = questionId;
    document.querySelector('#deleteModal p').innerHTML = 
        `Are you sure you want to delete the question "${questionText}..."? This action cannot be undone.`;
    document.getElementById('deleteModal').classList.add('modal-open');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.remove('modal-open');
    questionToDelete = null;
}

function deleteQuestion() {
    if (questionToDelete) {
        // TODO: Implement delete functionality
        console.log('Delete question:', questionToDelete);
        closeDeleteModal();
    }
}

// Close modal on successful form submission
document.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.xhr.status === 200 && event.detail.target.id === 'questionFormContainer') {
        const response = JSON.parse(event.detail.xhr.responseText);
        if (response.success) {
            closeAddQuestionModal();
            location.reload(); // Refresh to show new question
        }
    }
});
</script>

<style>
.modal {
    @apply fixed inset-0 z-50 hidden items-center justify-center;
}

.modal-open {
    @apply flex;
}

.modal-backdrop {
    @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-box {
    @apply relative bg-base-100 rounded-lg shadow-xl p-6 m-4 max-h-screen overflow-y-auto;
}
</style>
{% endblock %}
