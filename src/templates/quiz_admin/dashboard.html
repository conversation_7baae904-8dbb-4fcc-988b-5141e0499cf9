{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h1 class="my-h1">Quiz Management Dashboard</h1>
                    <p class="my-p">Create, manage, and track your quizzes</p>
                </div>
                <a href="{% url 'quiz_admin:quiz_create' %}" class="btn btn-primary">
                    <i class="fa-regular fa-plus"></i>
                    Create New Quiz
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="my-card text-center">
                <div class="text-3xl font-bold text-primary mb-2">{{ total_quizzes }}</div>
                <div class="my-p text-base-content/60">Total Quizzes</div>
            </div>
            <div class="my-card text-center">
                <div class="text-3xl font-bold text-success mb-2">{{ published_quizzes }}</div>
                <div class="my-p text-base-content/60">Published</div>
            </div>
            <div class="my-card text-center">
                <div class="text-3xl font-bold text-warning mb-2">{{ draft_quizzes }}</div>
                <div class="my-p text-base-content/60">Drafts</div>
            </div>
            <div class="my-card text-center">
                <div class="text-3xl font-bold text-info mb-2">{{ completion_rate|floatformat:1 }}%</div>
                <div class="my-p text-base-content/60">Completion Rate</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="my-card">
            <h2 class="my-h2 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{% url 'quiz_admin:quiz_create' %}"
                   class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 transition-colors">
                    <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-plus text-primary text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Create Quiz</div>
                        <div class="my-p text-base-content/60">Start a new quiz</div>
                    </div>
                </a>

                <a href="{% url 'quiz_admin:category_list' %}"
                   class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 transition-colors">
                    <div class="w-12 h-12 bg-success/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-folder text-success text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Manage Categories</div>
                        <div class="my-p text-base-content/60">Organize your quizzes</div>
                    </div>
                </a>
                
                <a href="{% url 'quiz_app:quiz_list' %}" 
                   class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 transition-colors">
                    <div class="w-12 h-12 bg-info/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-list text-info text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">View All Quizzes</div>
                        <div class="my-p text-base-content/60">Public quiz list</div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Recent Quizzes -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="my-h2">Your Quizzes</h2>
                <div class="flex gap-2">
                    <a href="{% url 'quiz_admin:quiz_create' %}" class="btn btn-sm btn-primary">
                        <i class="fa-regular fa-plus"></i>
                        New Quiz
                    </a>
                </div>
            </div>

            {% if quizzes %}
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>Quiz</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Questions</th>
                            <th>Participants</th>
                            <th>Avg Score</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for quiz in quizzes %}
                        <tr>
                            <td>
                                <div>
                                    <div class="my-h3">{{ quiz.title }}</div>
                                    <div class="my-p text-base-content/60">{{ quiz.description|truncatewords:8 }}</div>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-outline">{{ quiz.category.name }}</span>
                            </td>
                            <td>
                                {% if quiz.is_published %}
                                <span class="badge badge-success">Published</span>
                                {% else %}
                                <span class="badge badge-warning">Draft</span>
                                {% endif %}
                            </td>
                            <td>{{ quiz.total_questions }}</td>
                            <td>{{ quiz.analytics.total_participants|default:0 }}</td>
                            <td>{{ quiz.analytics.average_score|default:0|floatformat:1 }}%</td>
                            <td>
                                <div class="flex gap-1">
                                    <a href="{% url 'quiz_admin:quiz_detail' pk=quiz.pk %}" 
                                       class="btn btn-xs btn-primary" title="View Details">
                                        <i class="fa-regular fa-eye"></i>
                                    </a>
                                    <a href="{% url 'quiz_admin:quiz_edit' pk=quiz.pk %}" 
                                       class="btn btn-xs btn-outline" title="Edit">
                                        <i class="fa-regular fa-edit"></i>
                                    </a>
                                    <a href="{% url 'quiz_admin:send_invitations' quiz_id=quiz.pk %}" 
                                       class="btn btn-xs btn-success" title="Invite">
                                        <i class="fa-regular fa-envelope"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <div class="flex justify-center mt-6">
                <div class="join">
                    {% if page_obj.has_previous %}
                        <a href="?page=1" class="join-item btn">«</a>
                        <a href="?page={{ page_obj.previous_page_number }}" class="join-item btn">‹</a>
                    {% endif %}
                    
                    <span class="join-item btn btn-active">{{ page_obj.number }}</span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="join-item btn">›</a>
                        <a href="?page={{ page_obj.paginator.num_pages }}" class="join-item btn">»</a>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            {% else %}
            <div class="text-center py-12">
                <i class="fa-regular fa-clipboard-question text-6xl text-base-content/30 mb-4"></i>
                <h3 class="my-h2 mb-2">No Quizzes Yet</h3>
                <p class="my-p mb-6">Create your first quiz to get started!</p>
                <a href="{% url 'quiz_admin:quiz_create' %}" class="btn btn-primary">
                    <i class="fa-regular fa-plus"></i>
                    Create Your First Quiz
                </a>
            </div>
            {% endif %}
        </div>
    </main>
</div>
{% endblock %}
