{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen bg-base-200">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center gap-4 mb-4">
                <a href="{% url 'quiz_admin:dashboard' %}" class="btn btn-ghost btn-sm">
                    <i class="fa-regular fa-arrow-left"></i>
                    Back to Dashboard
                </a>
                <div>
                    <h1 class="my-h1">
                        {% if object %}Edit Category{% else %}Create New Category{% endif %}
                    </h1>
                    <p class="my-p">
                        {% if object %}
                            Update "{{ object.name }}" category details
                        {% else %}
                            Create a category to organize your quizzes
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>

        <!-- Category Form -->
        <form method="post" x-data="categoryForm()">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Category Information</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Category Name *</span>
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="text-error text-xs mt-1">{{ form.name.errors.0 }}</div>
                        {% endif %}
                        <div class="text-xs text-base-content/60 mt-1">
                            Choose a descriptive name for your category (e.g., "Python Programming", "Web Development")
                        </div>
                    </div>
                    
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Description</span>
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="text-error text-xs mt-1">{{ form.description.errors.0 }}</div>
                        {% endif %}
                        <div class="text-xs text-base-content/60 mt-1">
                            Optional description to help users understand what this category covers
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="my-card" x-show="categoryName.length > 0">
                <h2 class="my-h2 mb-4">Preview</h2>
                <div class="bg-base-200 p-4 rounded border-l-4 border-primary">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                            <i class="fa-regular fa-folder text-primary text-xl"></i>
                        </div>
                        <div>
                            <div class="my-h3" x-text="categoryName || 'Category Name'"></div>
                            <div class="my-p text-base-content/60" x-text="categoryDescription || 'No description provided'"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="my-card">
                <div class="flex flex-col sm:flex-row gap-4 justify-end">
                    <a href="{% url 'quiz_admin:dashboard' %}" class="btn btn-ghost">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary" :disabled="categoryName.length === 0">
                        <i class="fa-regular fa-save"></i>
                        {% if object %}Update Category{% else %}Create Category{% endif %}
                    </button>
                </div>
            </div>
        </form>
    </main>
</div>

<!-- Alpine.js Component -->
<script>
function categoryForm() {
    return {
        categoryName: '',
        categoryDescription: '',
        
        init() {
            // Style form inputs
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.classList.add('my-input');
                
                // Bind to Alpine data
                if (input.name === 'name') {
                    input.addEventListener('input', (e) => {
                        this.categoryName = e.target.value;
                    });
                    this.categoryName = input.value;
                }
                
                if (input.name === 'description') {
                    input.addEventListener('input', (e) => {
                        this.categoryDescription = e.target.value;
                    });
                    this.categoryDescription = input.value;
                }
            });
        }
    }
}
</script>

<style>
.my-input {
    @apply w-full border-1 border-neutral-content rounded-sm p-3 outline-none focus:outline-none focus:ring-0 focus:border-primary;
}

textarea.my-input {
    min-height: 100px;
    resize: vertical;
}
</style>
{% endblock %}
