{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen ">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center gap-4 mb-4">
                <a href="{% url 'quiz_admin:dashboard' %}" class="btn btn-ghost btn-sm">
                    <i class="fa-regular fa-arrow-left"></i>
                    Back to Dashboard
                </a>
                <div>
                    <h1 class="my-h1">
                        {% if object %}Edit Quiz{% else %}Create New Quiz{% endif %}
                    </h1>
                    <p class="my-p">
                        {% if object %}
                            Update the settings for "{{ object.title }}"
                        {% else %}
                            Set up your quiz configuration and settings
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>

        <!-- Quiz Form -->
        <form method="post" x-data="quizForm()">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Basic Information</h2>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Quiz Title *</span>
                        </label>
                        {{ form.title }}
                        {% if form.title.errors %}
                        <div class="text-error text-xs mt-1">{{ form.title.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Category *</span>
                        </label>
                        {{ form.category }}
                        {% if form.category.errors %}
                        <div class="text-error text-xs mt-1">{{ form.category.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block mb-2">
                        <span class="my-p font-medium">Description *</span>
                    </label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="text-error text-xs mt-1">{{ form.description.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Quiz Settings -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Quiz Settings</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Time Limit (minutes)</span>
                        </label>
                        {{ form.time_limit }}
                        <div class="text-xs text-base-content/60 mt-1">Leave blank for no time limit</div>
                        {% if form.time_limit.errors %}
                        <div class="text-error text-xs mt-1">{{ form.time_limit.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Max Questions</span>
                        </label>
                        {{ form.max_questions }}
                        <div class="text-xs text-base-content/60 mt-1">Leave blank to use all questions</div>
                        {% if form.max_questions.errors %}
                        <div class="text-error text-xs mt-1">{{ form.max_questions.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Pass Mark (%)</span>
                        </label>
                        {{ form.pass_mark }}
                        {% if form.pass_mark.errors %}
                        <div class="text-error text-xs mt-1">{{ form.pass_mark.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Checkboxes -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div class="space-y-4">
                        <label class="flex items-center gap-3">
                            {{ form.random_order }}
                            <span class="my-p">Randomize question order</span>
                        </label>
                        
                        <label class="flex items-center gap-3">
                            {{ form.single_attempt }}
                            <span class="my-p">Allow only one attempt per user</span>
                        </label>
                    </div>
                    
                    <div class="space-y-4">
                        <label class="flex items-center gap-3">
                            {{ form.show_answers_at_end }}
                            <span class="my-p">Show answers at end of quiz</span>
                        </label>
                        
                        <label class="flex items-center gap-3">
                            {{ form.show_correct_answers }}
                            <span class="my-p">Highlight correct answers</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Messages -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Custom Messages</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Success Message</span>
                        </label>
                        {{ form.success_text }}
                        <div class="text-xs text-base-content/60 mt-1">Message shown when user passes the quiz</div>
                        {% if form.success_text.errors %}
                        <div class="text-error text-xs mt-1">{{ form.success_text.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block mb-2">
                            <span class="my-p font-medium">Failure Message</span>
                        </label>
                        {{ form.fail_text }}
                        <div class="text-xs text-base-content/60 mt-1">Message shown when user fails the quiz</div>
                        {% if form.fail_text.errors %}
                        <div class="text-error text-xs mt-1">{{ form.fail_text.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Publication Status -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Publication</h2>
                <label class="flex items-center gap-3">
                    {{ form.is_published }}
                    <div>
                        <span class="my-p font-medium">Publish this quiz</span>
                        <div class="text-xs text-base-content/60">Published quizzes are visible to all users</div>
                    </div>
                </label>
            </div>

            <!-- Form Actions -->
            <div class="my-card">
                <div class="flex flex-col sm:flex-row gap-4 justify-end">
                    <a href="{% url 'quiz_admin:dashboard' %}" class="btn btn-ghost">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa-regular fa-save"></i>
                        {% if object %}Update Quiz{% else %}Create Quiz{% endif %}
                    </button>
                </div>
            </div>
        </form>
    </main>
</div>

<!-- Alpine.js Form Component -->
<script>
function quizForm() {
    return {
        init() {
            // Add form styling
            this.styleFormFields();
        },
        
        styleFormFields() {
            // Style all form inputs
            const inputs = document.querySelectorAll('input[type="text"], input[type="number"], textarea, select');
            inputs.forEach(input => {
                input.classList.add('my-input');
            });
            
            // Style checkboxes
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.classList.add('checkbox', 'checkbox-primary');
            });
        }
    }
}
</script>

<style>
/* Custom form styling */
.my-input {
    @apply w-full border-1 border-neutral-content rounded-sm p-3 outline-none focus:outline-none focus:ring-0 focus:border-primary;
}

textarea.my-input {
    min-height: 100px;
    resize: vertical;
}

select.my-input {
    @apply select select-bordered;
}
</style>
{% endblock %}
