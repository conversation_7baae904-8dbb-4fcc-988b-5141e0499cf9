{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen ">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center gap-4 mb-4">
                <a href="{% url 'quiz_admin:quiz_detail' pk=quiz.pk %}" class="btn btn-ghost btn-sm">
                    <i class="fa-regular fa-arrow-left"></i>
                    Back to Quiz
                </a>
                <div>
                    <h1 class="my-h1">Send Invitations</h1>
                    <p class="my-p">Invite participants to take "{{ quiz.title }}"</p>
                </div>
            </div>
        </div>

        <!-- Quiz Info -->
        <div class="my-card">
            <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <i class="fa-regular fa-clipboard-question text-primary text-xl"></i>
                </div>
                <div class="flex-1">
                    <h2 class="my-h2">{{ quiz.title }}</h2>
                    <p class="my-p text-base-content/60 mb-2">{{ quiz.description|truncatewords:20 }}</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="badge badge-outline">{{ quiz.category.name }}</span>
                        <span class="badge badge-outline">{{ quiz.total_questions }} questions</span>
                        {% if quiz.time_limit %}
                        <span class="badge badge-outline">{{ quiz.time_limit }} minutes</span>
                        {% endif %}
                        <span class="badge badge-outline">Pass: {{ quiz.pass_mark }}%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invitation Form -->
        <form method="post" x-data="invitationForm()">
            {% csrf_token %}
            
            <div class="my-card">
                <h2 class="my-h2 mb-4">Invitation Details</h2>
                
                <!-- Email Addresses -->
                <div class="mb-6">
                    <label class="block mb-2">
                        <span class="my-p font-medium">Email Addresses *</span>
                    </label>
                    <textarea 
                        name="emails" 
                        rows="6" 
                        class="my-input"
                        placeholder="Enter email addresses (one per line or comma-separated)&#10;<EMAIL>&#10;<EMAIL>, <EMAIL>"
                        required
                        x-model="emails"></textarea>
                    <div class="text-xs text-base-content/60 mt-1">
                        <i class="fa-regular fa-info-circle"></i>
                        Enter one email per line or separate multiple emails with commas
                    </div>
                    <div class="mt-2" x-show="emailCount > 0">
                        <span class="badge badge-info" x-text="`${emailCount} email(s) to invite`"></span>
                    </div>
                </div>

                <!-- Personal Message -->
                <div class="mb-6">
                    <label class="block mb-2">
                        <span class="my-p font-medium">Personal Message (Optional)</span>
                    </label>
                    <textarea 
                        name="message" 
                        rows="4" 
                        class="my-input"
                        placeholder="Add a personal message to your invitation..."
                        x-model="message"></textarea>
                    <div class="text-xs text-base-content/60 mt-1">
                        This message will be included with the quiz invitation
                    </div>
                </div>

                <!-- Expiration -->
                <div class="mb-6">
                    <label class="block mb-2">
                        <span class="my-p font-medium">Invitation Expires</span>
                    </label>
                    <select name="expires_days" class="my-input w-auto">
                        <option value="1">1 day</option>
                        <option value="3">3 days</option>
                        <option value="7" selected>1 week</option>
                        <option value="14">2 weeks</option>
                        <option value="30">1 month</option>
                    </select>
                    <div class="text-xs text-base-content/60 mt-1">
                        How long the invitation link will remain valid
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="my-card" x-show="emails.length > 0">
                <h2 class="my-h2 mb-4">Invitation Preview</h2>
                <div class="bg-base-200 p-4 rounded border-l-4 border-primary">
                    <div class="mb-3">
                        <div class="my-h3">You're invited to take a quiz!</div>
                        <div class="my-p text-base-content/60">{{ quiz.title }}</div>
                    </div>
                    
                    <div class="my-p mb-3">{{ quiz.description|truncatewords:30 }}</div>
                    
                    <div x-show="message.length > 0" class="mb-3">
                        <div class="my-p font-medium">Personal message:</div>
                        <div class="my-p italic" x-text="message"></div>
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mb-3">
                        <span class="badge badge-outline">{{ quiz.total_questions }} questions</span>
                        {% if quiz.time_limit %}
                        <span class="badge badge-outline">{{ quiz.time_limit }} minutes</span>
                        {% endif %}
                        <span class="badge badge-outline">Pass mark: {{ quiz.pass_mark }}%</span>
                    </div>
                    
                    <div class="btn btn-primary btn-sm">
                        <i class="fa-regular fa-play"></i>
                        Start Quiz
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="my-card">
                <div class="flex flex-col sm:flex-row gap-4 justify-between">
                    <div class="flex items-center gap-2 text-base-content/60">
                        <i class="fa-regular fa-shield-check"></i>
                        <span class="text-sm">Invitations are sent securely and can be tracked</span>
                    </div>
                    
                    <div class="flex gap-2">
                        <a href="{% url 'quiz_admin:quiz_detail' pk=quiz.pk %}" class="btn btn-ghost">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" :disabled="emailCount === 0">
                            <i class="fa-regular fa-envelope"></i>
                            <span x-text="emailCount > 0 ? `Send ${emailCount} Invitation(s)` : 'Send Invitations'"></span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </main>
</div>

<!-- Alpine.js Component -->
<script>
function invitationForm() {
    return {
        emails: '',
        message: '',
        
        get emailCount() {
            if (!this.emails.trim()) return 0;
            
            // Split by newlines and commas, filter out empty strings
            const emailList = this.emails
                .replace(/,/g, '\n')
                .split('\n')
                .map(email => email.trim())
                .filter(email => email.length > 0);
            
            return emailList.length;
        },
        
        init() {
            // Style form inputs
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                if (!input.classList.contains('my-input')) {
                    input.classList.add('my-input');
                }
            });
        }
    }
}
</script>

<style>
.my-input {
    @apply w-full border-1 border-neutral-content rounded-sm p-3 outline-none focus:outline-none focus:ring-0 focus:border-primary;
}

textarea.my-input {
    resize: vertical;
}

select.my-input {
    @apply select select-bordered;
}
</style>
{% endblock %}
