{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen bg-base-200">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-4">
                    <a href="{% url 'quiz_admin:category_list' %}" class="btn btn-ghost btn-sm">
                        <i class="fa-regular fa-arrow-left"></i>
                        Categories
                    </a>
                    <div>
                        <div class="flex items-center gap-2 mb-1">
                            <h1 class="my-h1">{{ category.name }}</h1>
                            {% if category.created_by == user %}
                            <span class="badge badge-success">Owner</span>
                            {% else %}
                            <span class="badge badge-info">Manager</span>
                            {% endif %}
                        </div>
                        <p class="my-p text-base-content/60">Category Management</p>
                    </div>
                </div>
                
                <div class="flex gap-2">
                    {% if category.created_by == user %}
                    <a href="{% url 'quiz_admin:category_edit' pk=category.pk %}" class="btn btn-outline">
                        <i class="fa-regular fa-edit"></i>
                        Edit
                    </a>
                    {% endif %}
                    {% if permissions.can_create_quizzes %}
                    <a href="{% url 'quiz_admin:quiz_create' %}" class="btn btn-primary">
                        <i class="fa-regular fa-plus"></i>
                        Create Quiz
                    </a>
                    {% endif %}
                </div>
            </div>
            
            {% if category.description %}
            <p class="my-p">{{ category.description }}</p>
            {% endif %}
        </div>

        <!-- Category Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-primary">{{ total_quizzes }}</div>
                <div class="text-xs text-base-content/60">Total Quizzes</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-success">{{ published_quizzes }}</div>
                <div class="text-xs text-base-content/60">Published</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-info">{{ total_questions }}</div>
                <div class="text-xs text-base-content/60">Total Questions</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-warning">{{ managers.count|default:0 }}</div>
                <div class="text-xs text-base-content/60">Managers</div>
            </div>
        </div>

        <!-- Action Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% if permissions.can_create_quizzes %}
            <a href="{% url 'quiz_admin:quiz_create' %}" 
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-plus text-primary text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Create Quiz</div>
                        <div class="my-p text-base-content/60">Add a new quiz to this category</div>
                    </div>
                </div>
            </a>
            {% endif %}
            
            {% if category.created_by == user %}
            <a href="{% url 'quiz_admin:category_managers' category_id=category.pk %}" 
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-success/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-users text-success text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Manage Managers</div>
                        <div class="my-p text-base-content/60">Invite and manage category managers</div>
                    </div>
                </div>
            </a>
            
            <a href="{% url 'quiz_admin:invite_category_manager' category_id=category.pk %}" 
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-info/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-envelope text-info text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Invite Manager</div>
                        <div class="my-p text-base-content/60">Invite a new category manager</div>
                    </div>
                </div>
            </a>
            {% endif %}
        </div>

        <!-- Quizzes in Category -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <h2 class="my-h2">Quizzes in this Category</h2>
                {% if permissions.can_create_quizzes %}
                <a href="{% url 'quiz_admin:quiz_create' %}" class="btn btn-sm btn-primary">
                    <i class="fa-regular fa-plus"></i>
                    New Quiz
                </a>
                {% endif %}
            </div>

            {% if quizzes %}
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>Quiz</th>
                            <th>Status</th>
                            <th>Questions</th>
                            <th>Participants</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for quiz in quizzes %}
                        <tr>
                            <td>
                                <div>
                                    <div class="my-h3">{{ quiz.title }}</div>
                                    <div class="my-p text-base-content/60">{{ quiz.description|truncatewords:8 }}</div>
                                </div>
                            </td>
                            <td>
                                {% if quiz.is_published %}
                                <span class="badge badge-success">Published</span>
                                {% else %}
                                <span class="badge badge-warning">Draft</span>
                                {% endif %}
                            </td>
                            <td>{{ quiz.total_questions }}</td>
                            <td>
                                {% if quiz.analytics %}
                                {{ quiz.analytics.total_participants|default:0 }}
                                {% else %}
                                0
                                {% endif %}
                            </td>
                            <td>{{ quiz.created_at|date:"M d, Y" }}</td>
                            <td>
                                <div class="flex gap-1">
                                    <a href="{% url 'quiz_admin:quiz_detail' pk=quiz.pk %}" 
                                       class="btn btn-xs btn-primary" title="View Details">
                                        <i class="fa-regular fa-eye"></i>
                                    </a>
                                    {% if permissions.can_edit_quizzes %}
                                    <a href="{% url 'quiz_admin:quiz_edit' pk=quiz.pk %}" 
                                       class="btn btn-xs btn-outline" title="Edit">
                                        <i class="fa-regular fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    {% if permissions.can_invite_participants %}
                                    <a href="{% url 'quiz_admin:send_invitations' quiz_id=quiz.pk %}" 
                                       class="btn btn-xs btn-success" title="Invite">
                                        <i class="fa-regular fa-envelope"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <i class="fa-regular fa-clipboard-question text-6xl text-base-content/30 mb-4"></i>
                <h3 class="my-h2 mb-2">No Quizzes Yet</h3>
                <p class="my-p mb-6">Create your first quiz in this category!</p>
                {% if permissions.can_create_quizzes %}
                <a href="{% url 'quiz_admin:quiz_create' %}" class="btn btn-primary">
                    <i class="fa-regular fa-plus"></i>
                    Create First Quiz
                </a>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Managers (Owner Only) -->
        {% if category.created_by == user and managers %}
        <div class="my-card">
            <h2 class="my-h2 mb-4">Category Managers</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {% for manager in managers %}
                <div class="flex items-center justify-between p-4 bg-base-200 rounded">
                    <div>
                        <div class="my-p font-medium">{{ manager.user.email }}</div>
                        <div class="text-xs text-base-content/60">
                            {{ manager.get_status_display }} • 
                            {% if manager.status == 'ACCEPTED' %}
                                Accepted {{ manager.responded_at|date:"M d" }}
                            {% else %}
                                Invited {{ manager.created_at|date:"M d" }}
                            {% endif %}
                        </div>
                    </div>
                    <span class="badge 
                               {% if manager.status == 'ACCEPTED' %}badge-success
                               {% elif manager.status == 'PENDING' %}badge-warning
                               {% elif manager.status == 'DECLINED' %}badge-error
                               {% else %}badge-neutral{% endif %}">
                        {{ manager.get_status_display }}
                    </span>
                </div>
                {% endfor %}
            </div>
            <div class="mt-4 text-center">
                <a href="{% url 'quiz_admin:category_managers' category_id=category.pk %}" class="btn btn-outline btn-sm">
                    <i class="fa-regular fa-cog"></i>
                    Manage All Managers
                </a>
            </div>
        </div>
        {% endif %}
    </main>
</div>
{% endblock %}
