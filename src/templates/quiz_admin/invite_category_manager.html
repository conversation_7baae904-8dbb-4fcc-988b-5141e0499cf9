{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen bg-base-200">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center gap-4 mb-4">
                <a href="{% url 'quiz_admin:category_managers' category_id=category.pk %}" class="btn btn-ghost btn-sm">
                    <i class="fa-regular fa-arrow-left"></i>
                    Back to Managers
                </a>
                <div>
                    <h1 class="my-h1">Invite Category Manager</h1>
                    <p class="my-p">Invite a user to help manage "{{ category.name }}"</p>
                </div>
            </div>
        </div>

        <!-- Category Info -->
        <div class="my-card">
            <div class="flex items-start gap-4">
                <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                    <i class="fa-regular fa-folder text-primary text-xl"></i>
                </div>
                <div class="flex-1">
                    <h2 class="my-h2">{{ category.name }}</h2>
                    <p class="my-p text-base-content/60 mb-2">{{ category.description|default:"No description provided" }}</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="badge badge-outline">Category</span>
                        <span class="badge badge-outline">{{ category.quizzes.count }} quiz{{ category.quizzes.count|pluralize }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Manager Invitation Form -->
        <form method="post" x-data="managerForm()">
            {% csrf_token %}
            
            <div class="my-card">
                <h2 class="my-h2 mb-4">Manager Details</h2>
                
                <!-- Email Address -->
                <div class="mb-6">
                    <label class="block mb-2">
                        <span class="my-p font-medium">User Email *</span>
                    </label>
                    <input 
                        type="email" 
                        name="email" 
                        class="my-input"
                        placeholder="Enter the email address of the user to invite"
                        required
                        x-model="email">
                    <div class="text-xs text-base-content/60 mt-1">
                        <i class="fa-regular fa-info-circle"></i>
                        The user must already have an account on the platform
                    </div>
                </div>
            </div>

            <!-- Permissions -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Manager Permissions</h2>
                <p class="my-p text-base-content/60 mb-4">Select what this manager can do with this category:</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <label class="flex items-start gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors">
                        <input type="checkbox" name="can_create_quizzes" class="checkbox checkbox-primary mt-1" checked>
                        <div>
                            <div class="my-p font-medium">Create Quizzes</div>
                            <div class="text-xs text-base-content/60">Can create new quizzes in this category</div>
                        </div>
                    </label>
                    
                    <label class="flex items-start gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors">
                        <input type="checkbox" name="can_edit_quizzes" class="checkbox checkbox-primary mt-1" checked>
                        <div>
                            <div class="my-p font-medium">Edit Quizzes</div>
                            <div class="text-xs text-base-content/60">Can edit existing quizzes in this category</div>
                        </div>
                    </label>
                    
                    <label class="flex items-start gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors">
                        <input type="checkbox" name="can_invite_participants" class="checkbox checkbox-primary mt-1" checked>
                        <div>
                            <div class="my-p font-medium">Invite Participants</div>
                            <div class="text-xs text-base-content/60">Can send quiz invitations</div>
                        </div>
                    </label>
                    
                    <label class="flex items-start gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors">
                        <input type="checkbox" name="can_view_analytics" class="checkbox checkbox-primary mt-1" checked>
                        <div>
                            <div class="my-p font-medium">View Analytics</div>
                            <div class="text-xs text-base-content/60">Can view quiz performance data</div>
                        </div>
                    </label>
                    
                    <label class="flex items-start gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors">
                        <input type="checkbox" name="can_manage_questions" class="checkbox checkbox-primary mt-1">
                        <div>
                            <div class="my-p font-medium">Manage Questions</div>
                            <div class="text-xs text-base-content/60">Can create and edit questions (advanced)</div>
                        </div>
                    </label>
                </div>
            </div>

            <!-- Preview -->
            <div class="my-card" x-show="email.length > 0">
                <h2 class="my-h2 mb-4">Invitation Preview</h2>
                <div class="bg-base-200 p-4 rounded border-l-4 border-primary">
                    <div class="mb-3">
                        <div class="my-h3">You're invited to manage a category!</div>
                        <div class="my-p text-base-content/60">{{ category.name }}</div>
                    </div>
                    
                    <div class="my-p mb-3">{{ category.description|default:"Help manage quizzes in this category." }}</div>
                    
                    <div class="flex flex-wrap gap-2 mb-3">
                        <span class="badge badge-outline">Category Manager</span>
                        <span class="badge badge-outline">{{ category.quizzes.count }} quiz{{ category.quizzes.count|pluralize }}</span>
                    </div>
                    
                    <div class="btn btn-primary btn-sm">
                        <i class="fa-regular fa-check"></i>
                        Accept Invitation
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="my-card">
                <div class="flex flex-col sm:flex-row gap-4 justify-between">
                    <div class="flex items-center gap-2 text-base-content/60">
                        <i class="fa-regular fa-shield-check"></i>
                        <span class="text-sm">Manager invitations are sent securely and can be revoked</span>
                    </div>
                    
                    <div class="flex gap-2">
                        <a href="{% url 'quiz_admin:category_managers' category_id=category.pk %}" class="btn btn-ghost">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" :disabled="email.length === 0">
                            <i class="fa-regular fa-envelope"></i>
                            Send Manager Invitation
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </main>
</div>

<!-- Alpine.js Component -->
<script>
function managerForm() {
    return {
        email: '',
        
        init() {
            // Style form inputs
            const inputs = document.querySelectorAll('input[type="email"]');
            inputs.forEach(input => {
                input.classList.add('my-input');
            });
        }
    }
}
</script>

<style>
.my-input {
    @apply w-full border-1 border-neutral-content rounded-sm p-3 outline-none focus:outline-none focus:ring-0 focus:border-primary;
}
</style>
{% endblock %}
