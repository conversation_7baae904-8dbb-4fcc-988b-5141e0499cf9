{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen ">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-4">
                    <a href="{% url 'quiz_admin:dashboard' %}" class="btn btn-ghost btn-sm">
                        <i class="fa-regular fa-arrow-left"></i>
                        Dashboard
                    </a>
                    <div>
                        <div class="flex items-center gap-2 mb-1">
                            <h1 class="my-h1">{{ quiz.title }}</h1>
                            {% if quiz.is_published %}
                            <span class="badge badge-success">Published</span>
                            {% else %}
                            <span class="badge badge-warning">Draft</span>
                            {% endif %}
                        </div>
                        <p class="my-p text-base-content/60">{{ quiz.category.name }}</p>
                    </div>
                </div>
                
                <div class="flex gap-2">
                    <a href="{% url 'quiz_admin:quiz_edit' pk=quiz.pk %}" class="btn btn-outline">
                        <i class="fa-regular fa-edit"></i>
                        Edit
                    </a>
                    <a href="{% url 'quiz_admin:send_invitations' quiz_id=quiz.pk %}" class="btn btn-primary">
                        <i class="fa-regular fa-envelope"></i>
                        Invite Participants
                    </a>
                </div>
            </div>
            
            <p class="my-p">{{ quiz.description }}</p>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-primary">{{ analytics.total_participants }}</div>
                <div class="text-xs text-base-content/60">Participants</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-success">{{ analytics.total_completions }}</div>
                <div class="text-xs text-base-content/60">Completions</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-info">{{ analytics.average_score|floatformat:1 }}%</div>
                <div class="text-xs text-base-content/60">Avg Score</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-warning">{{ analytics.total_invitations_sent }}</div>
                <div class="text-xs text-base-content/60">Invitations</div>
            </div>
        </div>

        <!-- Action Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
            <a href="{% url 'question_manager:quiz_questions' quiz_id=quiz.pk %}"
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-clipboard-question text-accent text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Manage Questions</div>
                        <div class="my-p text-base-content/60">Add and edit quiz questions</div>
                    </div>
                </div>
            </a>

            <a href="{% url 'quiz_admin:send_invitations' quiz_id=quiz.pk %}"
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-envelope text-primary text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Send Invitations</div>
                        <div class="my-p text-base-content/60">Invite users to take this quiz</div>
                    </div>
                </div>
            </a>

            <a href="{% url 'quiz_admin:analytics' quiz_id=quiz.pk %}"
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-info/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-chart-bar text-info text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">View Analytics</div>
                        <div class="my-p text-base-content/60">Detailed performance metrics</div>
                    </div>
                </div>
            </a>

            <a href="{% url 'quiz_admin:manage_invitations' quiz_id=quiz.pk %}"
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-success/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-users text-success text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Manage Invitations</div>
                        <div class="my-p text-base-content/60">Track invitation status</div>
                    </div>
                </div>
            </a>

            <a href="{% url 'quiz_admin:quiz_managers' quiz_id=quiz.pk %}"
               class="my-card hover:bg-base-200 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-warning/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-user-gear text-warning text-xl"></i>
                    </div>
                    <div>
                        <div class="my-h3">Manage Managers</div>
                        <div class="my-p text-base-content/60">Invite quiz co-managers</div>
                    </div>
                </div>
            </a>
        </div>

        <!-- Quiz Settings -->
        <div class="my-card">
            <h2 class="my-h2 mb-4">Quiz Configuration</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="flex items-center gap-2">
                    <i class="fa-regular fa-clock text-primary"></i>
                    <span class="my-p">
                        Time Limit: {% if quiz.time_limit %}{{ quiz.time_limit }} minutes{% else %}No limit{% endif %}
                    </span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fa-regular fa-list-ol text-primary"></i>
                    <span class="my-p">
                        Questions: {% if quiz.max_questions %}{{ quiz.max_questions }} max{% else %}All ({{ quiz.total_questions }}){% endif %}
                    </span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fa-regular fa-target text-primary"></i>
                    <span class="my-p">Pass Mark: {{ quiz.pass_mark }}%</span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fa-regular fa-shuffle text-primary"></i>
                    <span class="my-p">
                        {% if quiz.random_order %}Random order{% else %}Fixed order{% endif %}
                    </span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fa-regular fa-repeat text-primary"></i>
                    <span class="my-p">
                        {% if quiz.single_attempt %}Single attempt{% else %}Multiple attempts{% endif %}
                    </span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fa-regular fa-eye text-primary"></i>
                    <span class="my-p">
                        {% if quiz.show_answers_at_end %}Show answers{% else %}Hide answers{% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Sessions -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Recent Sessions</h2>
                {% if recent_sessions %}
                <div class="space-y-3">
                    {% for session in recent_sessions %}
                    <div class="flex items-center justify-between p-3 bg-base-200 rounded">
                        <div>
                            <div class="my-p font-medium">{{ session.user.email }}</div>
                            <div class="text-xs text-base-content/60">
                                {{ session.started_at|date:"M d, H:i" }}
                                {% if session.is_complete %} - Completed{% else %} - In Progress{% endif %}
                            </div>
                        </div>
                        {% if session.is_complete %}
                        <div class="text-right">
                            <div class="text-sm font-medium 
                                        {% if session.passed %}text-success{% else %}text-warning{% endif %}">
                                {{ session.score_percentage|floatformat:1 }}%
                            </div>
                            <div class="text-xs text-base-content/60">
                                {% if session.passed %}Passed{% else %}Failed{% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fa-regular fa-clock text-4xl text-base-content/30 mb-2"></i>
                    <p class="my-p text-base-content/60">No sessions yet</p>
                </div>
                {% endif %}
            </div>

            <!-- Recent Invitations -->
            <div class="my-card">
                <h2 class="my-h2 mb-4">Recent Invitations</h2>
                {% if invitations %}
                <div class="space-y-3">
                    {% for invitation in invitations %}
                    <div class="flex items-center justify-between p-3 bg-base-200 rounded">
                        <div>
                            <div class="my-p font-medium">{{ invitation.email }}</div>
                            <div class="text-xs text-base-content/60">
                                Sent {{ invitation.created_at|date:"M d, H:i" }}
                            </div>
                        </div>
                        <span class="badge 
                                   {% if invitation.status == 'ACCEPTED' %}badge-success
                                   {% elif invitation.status == 'DECLINED' %}badge-error
                                   {% elif invitation.status == 'EXPIRED' %}badge-neutral
                                   {% else %}badge-warning{% endif %}">
                            {{ invitation.get_status_display }}
                        </span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fa-regular fa-envelope text-4xl text-base-content/30 mb-2"></i>
                    <p class="my-p text-base-content/60">No invitations sent</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Question Performance -->
        {% if question_stats %}
        <div class="my-card">
            <h2 class="my-h2 mb-4">Question Performance</h2>
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>Question</th>
                            <th>Total Answers</th>
                            <th>Correct</th>
                            <th>Accuracy</th>
                            <th>Difficulty</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stat in question_stats %}
                        <tr>
                            <td>
                                <div class="my-p">{{ stat.question.content|truncatewords:10 }}</div>
                            </td>
                            <td>{{ stat.total_answers }}</td>
                            <td>{{ stat.correct_answers }}</td>
                            <td>
                                <div class="flex items-center gap-2">
                                    <div class="w-16 bg-base-300 rounded-full h-2">
                                        <div class="bg-primary h-2 rounded-full" 
                                             style="width: {{ stat.accuracy }}%"></div>
                                    </div>
                                    <span class="text-sm">{{ stat.accuracy|floatformat:1 }}%</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge 
                                           {% if stat.accuracy >= 80 %}badge-success
                                           {% elif stat.accuracy >= 60 %}badge-info
                                           {% elif stat.accuracy >= 40 %}badge-warning
                                           {% else %}badge-error{% endif %}">
                                    {% if stat.accuracy >= 80 %}Easy
                                    {% elif stat.accuracy >= 60 %}Medium
                                    {% elif stat.accuracy >= 40 %}Hard
                                    {% else %}Very Hard{% endif %}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </main>
</div>
{% endblock %}
