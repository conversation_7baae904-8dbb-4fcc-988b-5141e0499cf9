{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen bg-base-200">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h1 class="my-h1">Manage Categories</h1>
                    <p class="my-p">Organize your quizzes by creating and managing categories</p>
                </div>
                <div class="flex gap-2">
                    <a href="{% url 'quiz_admin:dashboard' %}" class="btn btn-ghost btn-sm">
                        <i class="fa-regular fa-arrow-left"></i>
                        Dashboard
                    </a>
                    <a href="{% url 'quiz_admin:category_create' %}" class="btn btn-primary">
                        <i class="fa-regular fa-plus"></i>
                        Create Category
                    </a>
                </div>
            </div>
        </div>

        <!-- Categories Grid -->
        {% if categories %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for category in categories %}
            <div class="my-card hover:bg-base-200 transition-colors">
                <div class="flex flex-col h-full">
                    <!-- Category Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                                <i class="fa-regular fa-folder text-primary text-xl"></i>
                            </div>
                            <div>
                                <h3 class="my-h2">{{ category.name }}</h3>
                                {% if category.created_by == user %}
                                <span class="badge badge-success badge-sm">Owner</span>
                                {% else %}
                                <span class="badge badge-info badge-sm">Manager</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Category Description -->
                    <div class="flex-1 mb-4">
                        {% if category.description %}
                        <p class="my-p text-base-content/80">{{ category.description|truncatewords:15 }}</p>
                        {% else %}
                        <p class="my-p text-base-content/60 italic">No description provided</p>
                        {% endif %}
                    </div>
                    
                    <!-- Category Stats -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center p-3 bg-base-200 rounded">
                            <div class="text-lg font-bold text-primary">{{ category.quiz_count }}</div>
                            <div class="text-xs text-base-content/60">Total Quizzes</div>
                        </div>
                        <div class="text-center p-3 bg-base-200 rounded">
                            <div class="text-lg font-bold text-success">{{ category.published_quiz_count }}</div>
                            <div class="text-xs text-base-content/60">Published</div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex gap-2">
                        <a href="{% url 'quiz_admin:category_detail' pk=category.pk %}" 
                           class="btn btn-primary btn-sm flex-1">
                            <i class="fa-regular fa-eye"></i>
                            View
                        </a>
                        {% if category.created_by == user %}
                        <a href="{% url 'quiz_admin:category_edit' pk=category.pk %}" 
                           class="btn btn-outline btn-sm">
                            <i class="fa-regular fa-edit"></i>
                        </a>
                        <a href="{% url 'quiz_admin:category_managers' category_id=category.pk %}" 
                           class="btn btn-ghost btn-sm">
                            <i class="fa-regular fa-users"></i>
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="flex justify-center mt-8">
            <div class="join">
                {% if page_obj.has_previous %}
                    <a href="?page=1" class="join-item btn">«</a>
                    <a href="?page={{ page_obj.previous_page_number }}" class="join-item btn">‹</a>
                {% endif %}
                
                <span class="join-item btn btn-active">{{ page_obj.number }}</span>
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="join-item btn">›</a>
                    <a href="?page={{ page_obj.paginator.num_pages }}" class="join-item btn">»</a>
                {% endif %}
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="my-card text-center">
            <div class="py-12">
                <i class="fa-regular fa-folder-open text-6xl text-base-content/30 mb-4"></i>
                <h3 class="my-h2 mb-2">No Categories Yet</h3>
                <p class="my-p mb-6">Create your first category to organize your quizzes!</p>
                <a href="{% url 'quiz_admin:category_create' %}" class="btn btn-primary">
                    <i class="fa-regular fa-plus"></i>
                    Create Your First Category
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="my-card">
            <h2 class="my-h2 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{% url 'quiz_admin:category_create' %}" 
                   class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 transition-colors">
                    <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-plus text-primary"></i>
                    </div>
                    <div>
                        <div class="my-h3">Create Category</div>
                        <div class="my-p text-base-content/60">Add a new category</div>
                    </div>
                </a>
                
                <a href="{% url 'quiz_admin:quiz_create' %}" 
                   class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 transition-colors">
                    <div class="w-10 h-10 bg-success/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-clipboard-question text-success"></i>
                    </div>
                    <div>
                        <div class="my-h3">Create Quiz</div>
                        <div class="my-p text-base-content/60">Add a new quiz</div>
                    </div>
                </a>
                
                <a href="{% url 'quiz_admin:dashboard' %}" 
                   class="flex items-center gap-3 p-4 border border-base-300 rounded hover:bg-base-200 transition-colors">
                    <div class="w-10 h-10 bg-info/20 rounded-full flex items-center justify-center">
                        <i class="fa-regular fa-chart-line text-info"></i>
                    </div>
                    <div>
                        <div class="my-h3">Dashboard</div>
                        <div class="my-p text-base-content/60">View overview</div>
                    </div>
                </a>
            </div>
        </div>
    </main>
</div>
{% endblock %}
