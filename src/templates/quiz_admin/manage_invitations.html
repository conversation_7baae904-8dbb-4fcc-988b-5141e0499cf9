{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="min-h-screen ">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">
        <!-- Header -->
        <div class="my-card">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-4">
                    <a href="{% url 'quiz_admin:quiz_detail' pk=quiz.pk %}" class="btn btn-ghost btn-sm">
                        <i class="fa-regular fa-arrow-left"></i>
                        Back to Quiz
                    </a>
                    <div>
                        <h1 class="my-h1">Manage Invitations</h1>
                        <p class="my-p">Track and manage invitations for "{{ quiz.title }}"</p>
                    </div>
                </div>
                <a href="{% url 'quiz_admin:send_invitations' quiz_id=quiz.pk %}" class="btn btn-primary">
                    <i class="fa-regular fa-plus"></i>
                    Send More Invitations
                </a>
            </div>
        </div>

        <!-- Invitations List -->
        {% if invitations %}
        <div class="my-card">
            <h2 class="my-h2 mb-4">All Invitations</h2>
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Sent Date</th>
                            <th>Expires</th>
                            <th>Response Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invitation in invitations %}
                        <tr>
                            <td>
                                <div>
                                    <div class="my-p font-medium">{{ invitation.email }}</div>
                                    {% if invitation.invited_user %}
                                    <div class="text-xs text-base-content/60">
                                        Registered user: {{ invitation.invited_user.get_full_name|default:invitation.invited_user.username }}
                                    </div>
                                    {% else %}
                                    <div class="text-xs text-base-content/60">Unregistered user</div>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge 
                                           {% if invitation.status == 'ACCEPTED' %}badge-success
                                           {% elif invitation.status == 'DECLINED' %}badge-error
                                           {% elif invitation.status == 'EXPIRED' or invitation.is_expired %}badge-neutral
                                           {% else %}badge-warning{% endif %}">
                                    {% if invitation.is_expired and invitation.status == 'PENDING' %}
                                        Expired
                                    {% else %}
                                        {{ invitation.get_status_display }}
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="text-sm">{{ invitation.created_at|date:"M d, Y" }}</div>
                                <div class="text-xs text-base-content/60">{{ invitation.created_at|time:"H:i" }}</div>
                            </td>
                            <td>
                                <div class="text-sm {% if invitation.is_expired %}text-error{% endif %}">
                                    {{ invitation.expires_at|date:"M d, Y" }}
                                </div>
                                <div class="text-xs text-base-content/60">{{ invitation.expires_at|time:"H:i" }}</div>
                            </td>
                            <td>
                                {% if invitation.responded_at %}
                                <div class="text-sm">{{ invitation.responded_at|date:"M d, Y" }}</div>
                                <div class="text-xs text-base-content/60">{{ invitation.responded_at|time:"H:i" }}</div>
                                {% else %}
                                <span class="text-base-content/40">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="flex gap-1">
                                    {% if invitation.status == 'PENDING' and not invitation.is_expired %}
                                    <form method="post" class="inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="resend">
                                        <input type="hidden" name="invitation_id" value="{{ invitation.id }}">
                                        <button type="submit" class="btn btn-xs btn-outline" title="Resend">
                                            <i class="fa-regular fa-refresh"></i>
                                        </button>
                                    </form>
                                    <form method="post" class="inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="cancel">
                                        <input type="hidden" name="invitation_id" value="{{ invitation.id }}">
                                        <button type="submit" class="btn btn-xs btn-error" title="Cancel"
                                                onclick="return confirm('Cancel this invitation?')">
                                            <i class="fa-regular fa-times"></i>
                                        </button>
                                    </form>
                                    {% elif invitation.status == 'EXPIRED' or invitation.is_expired %}
                                    <form method="post" class="inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="resend">
                                        <input type="hidden" name="invitation_id" value="{{ invitation.id }}">
                                        <button type="submit" class="btn btn-xs btn-primary" title="Resend">
                                            <i class="fa-regular fa-envelope"></i>
                                        </button>
                                    </form>
                                    {% else %}
                                    <span class="text-base-content/40">-</span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Summary Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            {% with total=invitations.count pending=invitations|length accepted=invitations|length declined=invitations|length expired=invitations|length %}
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-primary">{{ total }}</div>
                <div class="text-xs text-base-content/60">Total Sent</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-warning">
                    {% for inv in invitations %}{% if inv.status == 'PENDING' and not inv.is_expired %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                </div>
                <div class="text-xs text-base-content/60">Pending</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-success">
                    {% for inv in invitations %}{% if inv.status == 'ACCEPTED' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                </div>
                <div class="text-xs text-base-content/60">Accepted</div>
            </div>
            <div class="my-card text-center">
                <div class="text-2xl font-bold text-error">
                    {% for inv in invitations %}{% if inv.status == 'DECLINED' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                </div>
                <div class="text-xs text-base-content/60">Declined</div>
            </div>
            {% endwith %}
        </div>

        {% else %}
        <div class="my-card text-center">
            <div class="py-12">
                <i class="fa-regular fa-envelope text-6xl text-base-content/30 mb-4"></i>
                <h3 class="my-h2 mb-2">No Invitations Sent</h3>
                <p class="my-p mb-6">Start inviting participants to take this quiz!</p>
                <a href="{% url 'quiz_admin:send_invitations' quiz_id=quiz.pk %}" class="btn btn-primary">
                    <i class="fa-regular fa-envelope"></i>
                    Send First Invitation
                </a>
            </div>
        </div>
        {% endif %}
    </main>
</div>
{% endblock %}
