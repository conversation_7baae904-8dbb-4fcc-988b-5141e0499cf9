#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to read and analyze the benchmark documents
"""
import os
import sys
from docx import Document

def read_docx_file(file_path):
    """Read content from a DOCX file."""
    try:
        doc = Document(file_path)
        content = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        
        # Also read tables if any
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    content.append(" | ".join(row_text))
        
        return content
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []

def analyze_benchmark_docs():
    """Analyze all benchmark documents."""
    base_path = "../reference docs/survey"
    
    documents = [
        "face-face Benchmark.docx",
        "phone Benchmark.docx", 
        "zoom Benchmark.docx"
    ]
    
    for doc_name in documents:
        file_path = os.path.join(base_path, doc_name)
        print(f"\n{'='*60}")
        print(f"ANALYZING: {doc_name}")
        print(f"{'='*60}")
        
        if os.path.exists(file_path):
            content = read_docx_file(file_path)
            
            if content:
                for i, line in enumerate(content, 1):
                    print(f"{i:3d}: {line}")
            else:
                print("No content found or error reading file")
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    analyze_benchmark_docs()
