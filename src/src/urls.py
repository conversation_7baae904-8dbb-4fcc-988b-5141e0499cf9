"""
URL configuration for src project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.views.generic import TemplateView
from django.contrib.auth.decorators import login_required

urlpatterns = [
    path("admin/", admin.site.urls),

    # Custom account URLs
    path("accounts/", include("src.all_urls.account_urls")),

    # django-allauth URLs (for any URLs not covered by our custom views)
    path("accounts/", include("allauth.urls")),

    # Quiz app URLs
    path("quiz/", include("quiz_app.urls")),

    # Quiz admin URLs
    path("quiz-admin/", include("quiz_admin.urls")),

    # Question manager URLs
    path("questions/", include("question_manager.urls")),

    # Home page
    path("", TemplateView.as_view(template_name="home.html"), name="home"),
]
