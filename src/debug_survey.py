#!/usr/bin/env python
"""
Debug script to test survey question rendering
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings')
django.setup()

from survey.models import Survey, SurveyQuestion
from survey.utils import SurveyQuestionRenderer

def test_question_rendering():
    """Test question rendering."""
    survey = Survey.objects.first()
    if not survey:
        print("No surveys found!")
        return
    
    print(f"Testing survey: {survey.title}")
    
    question = survey.questions.first()
    if not question:
        print("No questions found!")
        return
    
    print(f"Testing question: {question.question_text[:50]}...")
    print(f"Question type: {question.question_type}")
    print(f"Scale min/max: {question.scale_min}-{question.scale_max}")
    print(f"Scale labels: {question.scale_labels}")
    
    # Test the renderer
    context = SurveyQuestionRenderer.get_question_context(question)
    
    print(f"\nGenerated context keys: {list(context.keys())}")
    
    if 'scale_options' in context:
        print(f"Scale options generated: {len(context['scale_options'])}")
        for option in context['scale_options']:
            print(f"  - Value: {option['value']}, Label: {option['label']}")
    else:
        print("No scale_options in context!")
    
    return context

if __name__ == "__main__":
    test_question_rendering()
