from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from quiz_app.models import Quiz, Question, Category


class QuestionTemplate(models.Model):
    """Reusable question templates that can be added to multiple quizzes."""
    title = models.Char<PERSON>ield(max_length=200, help_text="Template name for organization")
    description = models.TextField(blank=True, help_text="Description of this question template")
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='question_templates')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='question_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Template settings
    is_public = models.BooleanField(default=False, help_text="Allow other users to use this template")
    tags = models.JSONField(default=list, blank=True, help_text="Tags for organizing templates")
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} (by {self.created_by.username})"


class BulkQuestionImport(models.Model):
    """Track bulk question import operations."""
    IMPORT_METHODS = [
        ('TEXT', 'Text Parsing'),
        ('CSV', 'CSV Upload'),
        ('MODAL', 'Single Question Modal'),
    ]
    
    IMPORT_STATUS = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
    ]
    
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='question_imports')
    imported_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='question_imports')
    method = models.CharField(max_length=20, choices=IMPORT_METHODS)
    status = models.CharField(max_length=20, choices=IMPORT_STATUS, default='PENDING')
    
    # Import details
    total_questions = models.PositiveIntegerField(default=0)
    successful_imports = models.PositiveIntegerField(default=0)
    failed_imports = models.PositiveIntegerField(default=0)
    
    # File storage for CSV imports
    source_file = models.FileField(upload_to='question_imports/', null=True, blank=True)
    
    # Error tracking
    error_log = models.JSONField(default=list, blank=True, help_text="List of import errors")
    
    # Timing
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-started_at']
    
    def __str__(self):
        return f"{self.method} import for {self.quiz.title} by {self.imported_by.username}"
    
    def mark_completed(self):
        """Mark import as completed."""
        self.status = 'COMPLETED'
        self.completed_at = timezone.now()
        self.save()
    
    def mark_failed(self, error_message):
        """Mark import as failed with error message."""
        self.status = 'FAILED'
        self.completed_at = timezone.now()
        self.error_log.append({
            'timestamp': timezone.now().isoformat(),
            'error': error_message
        })
        self.save()
    
    def add_error(self, error_message, line_number=None):
        """Add an error to the error log."""
        error_entry = {
            'timestamp': timezone.now().isoformat(),
            'error': error_message
        }
        if line_number:
            error_entry['line'] = line_number
        
        self.error_log.append(error_entry)
        self.failed_imports += 1
        self.save()


class QuestionBank(models.Model):
    """Organized collections of questions for reuse."""
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='question_banks')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='question_banks')
    
    # Access control
    is_public = models.BooleanField(default=False, help_text="Allow other users to use questions from this bank")
    collaborators = models.ManyToManyField(User, blank=True, related_name='shared_question_banks')
    
    # Organization
    tags = models.JSONField(default=list, blank=True)
    questions = models.ManyToManyField(Question, blank=True, related_name='question_banks')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-updated_at']
        unique_together = ['name', 'created_by']
    
    def __str__(self):
        return f"{self.name} (by {self.created_by.username})"
    
    @property
    def question_count(self):
        return self.questions.count()
    
    def user_can_access(self, user):
        """Check if user can access this question bank."""
        return (
            self.created_by == user or 
            self.is_public or 
            user in self.collaborators.all()
        )
