from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q, Count
from django.core.exceptions import PermissionDenied
from django.http import JsonResponse
from django.contrib.auth.models import User
import csv
import io
import re
import json

from quiz_app.models import Quiz, Question, Category, MCQChoice
from .models import QuestionTemplate, BulkQuestionImport, QuestionBank
from .utils import TextQuestionParser, CSVQuestionParser


@login_required
def quiz_questions(request, quiz_id):
    """Manage questions for a quiz."""
    quiz = get_object_or_404(Quiz, id=quiz_id)

    # Check if user can manage questions for this quiz
    permissions = quiz.get_user_permissions(request.user)
    if not permissions['can_manage_questions']:
        raise PermissionDenied("You don't have permission to manage questions for this quiz")

    questions = quiz.questions.all().order_by('created_at')

    # Get question banks user can access (simplified for now)
    accessible_banks = []

    context = {
        'quiz': quiz,
        'questions': questions,
        'permissions': permissions,
        'question_banks': accessible_banks,
    }

    return render(request, 'question_manager/quiz_questions.html', context)


@login_required
def add_question_modal(request, quiz_id):
    """Add a single question via modal form."""
    quiz = get_object_or_404(Quiz, id=quiz_id)
    
    # Check permissions
    permissions = quiz.get_user_permissions(request.user)
    if not permissions['can_manage_questions']:
        raise PermissionDenied("You don't have permission to manage questions for this quiz")
    
    if request.method == 'POST':
        try:
            question = create_question_from_form(request.POST, quiz, request.user)
            messages.success(request, f"Question added successfully!")
            
            if request.headers.get('HX-Request'):
                return JsonResponse({'success': True, 'message': 'Question added successfully!'})
            
        except Exception as e:
            messages.error(request, f"Error creating question: {str(e)}")
            
            if request.headers.get('HX-Request'):
                return JsonResponse({'success': False, 'error': str(e)})
        
        return redirect('question_manager:quiz_questions', quiz_id=quiz_id)
    
    context = {
        'quiz': quiz,
    }
    
    if request.headers.get('HX-Request'):
        return render(request, 'question_manager/partials/add_question_form.html', context)
    
    return render(request, 'question_manager/add_question_modal.html', context)


@login_required
def bulk_add_questions(request, quiz_id):
    """Add questions via text parsing or CSV upload."""
    quiz = get_object_or_404(Quiz, id=quiz_id)
    
    # Check permissions
    permissions = quiz.get_user_permissions(request.user)
    if not permissions['can_manage_questions']:
        raise PermissionDenied("You don't have permission to manage questions for this quiz")
    
    if request.method == 'POST':
        method = request.POST.get('method')
        
        # Create import record
        import_record = BulkQuestionImport.objects.create(
            quiz=quiz,
            imported_by=request.user,
            method=method.upper() if method else 'TEXT'
        )
        
        try:
            if method == 'text':
                questions_created = handle_text_questions(request, quiz, import_record)
            elif method == 'csv':
                questions_created = handle_csv_questions(request, quiz, import_record)
            else:
                raise ValueError("Invalid import method")
            
            import_record.successful_imports = questions_created
            import_record.mark_completed()
            
            messages.success(request, f"Successfully created {questions_created} question(s)!")
            
        except Exception as e:
            import_record.mark_failed(str(e))
            messages.error(request, f"Error importing questions: {str(e)}")
        
        return redirect('question_manager:quiz_questions', quiz_id=quiz_id)
    
    context = {
        'quiz': quiz,
    }
    
    return render(request, 'question_manager/bulk_add_questions.html', context)


def create_question_from_form(form_data, quiz, user):
    """Create a question from form data."""
    question_type = form_data.get('question_type')
    content = form_data.get('content', '').strip()
    explanation = form_data.get('explanation', '').strip()
    marks = int(form_data.get('marks', 1))
    
    if not content:
        raise ValueError("Question content is required.")
    
    # Create question
    question = Question.objects.create(
        question_type=question_type,
        content=content,
        explanation=explanation,
        marks=marks,
        category=quiz.category,
        created_by=user
    )
    
    # Add to quiz
    question.quiz.add(quiz)
    
    # Handle choices based on question type
    if question_type == 'MCQ':
        choices_data = []
        for i in range(1, 6):  # Support up to 5 choices
            choice_content = form_data.get(f'choice_{i}', '').strip()
            if choice_content:
                is_correct = form_data.get(f'correct_{i}') == 'on'
                choices_data.append({
                    'content': choice_content,
                    'is_correct': is_correct,
                    'order': i
                })
        
        # Validate at least one correct answer
        if not any(choice['is_correct'] for choice in choices_data):
            question.delete()
            raise ValueError("At least one choice must be marked as correct.")
        
        # Create choices
        for choice_data in choices_data:
            MCQChoice.objects.create(
                question=question,
                **choice_data
            )
    
    elif question_type == 'TF':
        # Create True/False choices
        correct_answer = form_data.get('tf_answer') == 'true'
        MCQChoice.objects.create(
            question=question,
            content='True',
            is_correct=correct_answer,
            order=1
        )
        MCQChoice.objects.create(
            question=question,
            content='False',
            is_correct=not correct_answer,
            order=2
        )
    
    return question


def handle_text_questions(request, quiz, import_record):
    """Handle text-based question import."""
    text_content = request.POST.get('text_content', '').strip()
    
    if not text_content:
        raise ValueError("Please provide question text.")
    
    parser = TextQuestionParser(quiz, request.user)
    questions_created = parser.parse(text_content)
    
    # Update import record
    import_record.total_questions = questions_created
    import_record.save()
    
    return questions_created


def handle_csv_questions(request, quiz, import_record):
    """Handle CSV-based question import."""
    csv_file = request.FILES.get('csv_file')
    
    if not csv_file:
        raise ValueError("Please upload a CSV file.")
    
    if not csv_file.name.endswith('.csv'):
        raise ValueError("Please upload a valid CSV file.")
    
    # Save file to import record
    import_record.source_file = csv_file
    import_record.save()
    
    parser = CSVQuestionParser(quiz, request.user, import_record)
    questions_created = parser.parse(csv_file)
    
    return questions_created


# ============================================================================
# QUESTION BANK VIEWS
# ============================================================================

class QuestionBankListView(LoginRequiredMixin, ListView):
    """List question banks accessible to the user."""
    model = QuestionBank
    template_name = 'question_manager/question_bank_list.html'
    context_object_name = 'question_banks'
    paginate_by = 12

    def get_queryset(self):
        user = self.request.user
        return QuestionBank.objects.filter(
            Q(created_by=user) |
            Q(is_public=True) |
            Q(collaborators=user)
        ).distinct().annotate(
            question_count=Count('questions')
        ).order_by('-updated_at')


class QuestionBankDetailView(LoginRequiredMixin, DetailView):
    """Detailed view of a question bank."""
    model = QuestionBank
    template_name = 'question_manager/question_bank_detail.html'
    context_object_name = 'question_bank'

    def get_queryset(self):
        user = self.request.user
        return QuestionBank.objects.filter(
            Q(created_by=user) |
            Q(is_public=True) |
            Q(collaborators=user)
        ).distinct()


class QuestionBankCreateView(LoginRequiredMixin, CreateView):
    """Create a new question bank."""
    model = QuestionBank
    template_name = 'question_manager/question_bank_form.html'
    fields = ['name', 'description', 'category', 'is_public', 'tags']

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, f"Question bank '{form.instance.name}' created successfully!")
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('question_manager:question_bank_detail', kwargs={'pk': self.object.pk})


class QuestionBankUpdateView(LoginRequiredMixin, UpdateView):
    """Update an existing question bank."""
    model = QuestionBank
    template_name = 'question_manager/question_bank_form.html'
    fields = ['name', 'description', 'category', 'is_public', 'tags', 'collaborators']

    def get_queryset(self):
        return QuestionBank.objects.filter(created_by=self.request.user)

    def form_valid(self, form):
        messages.success(self.request, f"Question bank '{form.instance.name}' updated successfully!")
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('question_manager:question_bank_detail', kwargs={'pk': self.object.pk})


# ============================================================================
# QUESTION TEMPLATE VIEWS
# ============================================================================

class QuestionTemplateListView(LoginRequiredMixin, ListView):
    """List question templates accessible to the user."""
    model = QuestionTemplate
    template_name = 'question_manager/question_template_list.html'
    context_object_name = 'templates'
    paginate_by = 12

    def get_queryset(self):
        user = self.request.user
        return QuestionTemplate.objects.filter(
            Q(created_by=user) | Q(is_public=True)
        ).distinct().order_by('-created_at')


class QuestionTemplateCreateView(LoginRequiredMixin, CreateView):
    """Create a new question template."""
    model = QuestionTemplate
    template_name = 'question_manager/question_template_form.html'
    fields = ['title', 'description', 'category', 'is_public', 'tags']

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, f"Question template '{form.instance.title}' created successfully!")
        return super().form_valid(form)

    def get_success_url(self):
        return reverse('question_manager:question_template_list')


# ============================================================================
# UTILITY VIEWS
# ============================================================================

class ImportHistoryView(LoginRequiredMixin, ListView):
    """View import history for the user."""
    model = BulkQuestionImport
    template_name = 'question_manager/import_history.html'
    context_object_name = 'imports'
    paginate_by = 20

    def get_queryset(self):
        return BulkQuestionImport.objects.filter(
            imported_by=self.request.user
        ).order_by('-started_at')


@login_required
def download_csv_template(request):
    """Download CSV template for question import."""
    from django.http import HttpResponse

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="question_import_template.csv"'

    from .utils import get_csv_template
    template_data = get_csv_template()

    if template_data:
        fieldnames = template_data[0].keys()
        writer = csv.DictWriter(response, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(template_data)

    return response
