from django.contrib import admin
from .models import QuestionTemplate, BulkQuestionImport, QuestionBank


@admin.register(QuestionTemplate)
class QuestionTemplateAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'created_by', 'is_public', 'created_at']
    list_filter = ['is_public', 'category', 'created_at']
    search_fields = ['title', 'description', 'created_by__username']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Template Info', {
            'fields': ('title', 'description', 'category', 'created_by')
        }),
        ('Settings', {
            'fields': ('is_public', 'tags')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(BulkQuestionImport)
class BulkQuestionImportAdmin(admin.ModelAdmin):
    list_display = ['quiz', 'imported_by', 'method', 'status', 'successful_imports', 'failed_imports', 'started_at']
    list_filter = ['method', 'status', 'started_at']
    search_fields = ['quiz__title', 'imported_by__username']
    readonly_fields = ['started_at', 'completed_at', 'error_log']
    
    fieldsets = (
        ('Import Details', {
            'fields': ('quiz', 'imported_by', 'method', 'status')
        }),
        ('Results', {
            'fields': ('total_questions', 'successful_imports', 'failed_imports')
        }),
        ('Files', {
            'fields': ('source_file',)
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at')
        }),
        ('Errors', {
            'fields': ('error_log',),
            'classes': ('collapse',)
        }),
    )


@admin.register(QuestionBank)
class QuestionBankAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'created_by', 'is_public', 'question_count', 'created_at']
    list_filter = ['is_public', 'category', 'created_at']
    search_fields = ['name', 'description', 'created_by__username']
    filter_horizontal = ['collaborators', 'questions']
    readonly_fields = ['created_at', 'updated_at', 'question_count']
    
    fieldsets = (
        ('Bank Info', {
            'fields': ('name', 'description', 'category', 'created_by')
        }),
        ('Access Control', {
            'fields': ('is_public', 'collaborators')
        }),
        ('Organization', {
            'fields': ('tags', 'questions')
        }),
        ('Metadata', {
            'fields': ('question_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
