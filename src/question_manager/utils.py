import re
import csv
import io
from django.utils import timezone
from quiz_app.models import Question, MCQChoice


class TextQuestionParser:
    """Parse questions from formatted text input."""
    
    def __init__(self, quiz, user):
        self.quiz = quiz
        self.user = user
        self.questions_created = 0
    
    def parse(self, text_content):
        """Parse questions from text content."""
        # Split by question blocks (MC:, SA:, TF:)
        blocks = re.split(r'\n(?=(?:MC|SA|TF):)', text_content.strip())
        
        for block in blocks:
            block = block.strip()
            if not block:
                continue
            
            try:
                self._parse_question_block(block)
            except Exception as e:
                # Log error but continue with other questions
                print(f"Error parsing question block: {e}")
                continue
        
        return self.questions_created
    
    def _parse_question_block(self, block):
        """Parse a single question block."""
        lines = [line.strip() for line in block.split('\n') if line.strip()]
        if len(lines) < 2:
            return
        
        # Parse question type and content
        first_line = lines[0]
        question_type, question_content, remaining_lines = self._parse_question_header(first_line, lines)
        
        if not question_type or not question_content:
            return
        
        # Create question
        question = Question.objects.create(
            question_type=question_type,
            content=question_content,
            category=self.quiz.category,
            created_by=self.user,
            marks=1
        )
        question.quiz.add(self.quiz)
        
        # Handle choices based on type
        self._create_question_choices(question, question_type, remaining_lines)
        
        self.questions_created += 1
    
    def _parse_question_header(self, first_line, lines):
        """Parse question type and content from header."""
        question_type = None
        question_content = None
        remaining_lines = lines[1:]
        
        if first_line.startswith('MC:'):
            question_type = 'MCQ'
            question_content = first_line[3:].strip()
        elif first_line.startswith('SA:'):
            question_type = 'SUBJECTIVE'
            question_content = first_line[3:].strip()
        elif first_line.startswith('TF:'):
            question_type = 'TF'
            question_content = first_line[3:].strip()
        
        # Extract question content if it's in brackets
        if question_content and question_content.startswith('[') and ']' in question_content:
            end_bracket = question_content.find(']')
            question_content = question_content[1:end_bracket]
        elif not question_content and len(lines) > 1:
            # Question content is on the next line
            question_content = lines[1]
            remaining_lines = lines[2:]
        
        return question_type, question_content, remaining_lines
    
    def _create_question_choices(self, question, question_type, choice_lines):
        """Create choices for the question based on type."""
        if question_type == 'MCQ':
            for i, choice_text in enumerate(choice_lines):
                if choice_text:
                    # First choice is correct by default
                    is_correct = (i == 0)
                    MCQChoice.objects.create(
                        question=question,
                        content=choice_text,
                        is_correct=is_correct,
                        order=i + 1
                    )
        
        elif question_type == 'TF':
            # Parse TRUE/FALSE from remaining lines
            correct_answer = True  # Default
            if choice_lines:
                answer_text = choice_lines[0].upper()
                correct_answer = answer_text in ['TRUE', 'T', 'YES', 'Y', '1']
            
            MCQChoice.objects.create(
                question=question,
                content='True',
                is_correct=correct_answer,
                order=1
            )
            MCQChoice.objects.create(
                question=question,
                content='False',
                is_correct=not correct_answer,
                order=2
            )


class CSVQuestionParser:
    """Parse questions from CSV file."""
    
    def __init__(self, quiz, user, import_record=None):
        self.quiz = quiz
        self.user = user
        self.import_record = import_record
        self.questions_created = 0
    
    def parse(self, csv_file):
        """Parse questions from CSV file."""
        # Read CSV content
        content = csv_file.read().decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(content))
        
        line_number = 1  # Header is line 1
        
        for row in csv_reader:
            line_number += 1
            try:
                self._parse_csv_row(row, line_number)
            except Exception as e:
                error_msg = f"Line {line_number}: {str(e)}"
                if self.import_record:
                    self.import_record.add_error(error_msg, line_number)
                print(error_msg)
                continue
        
        return self.questions_created
    
    def _parse_csv_row(self, row, line_number):
        """Parse a single CSV row."""
        question_type = row.get('type', '').upper()
        question_content = row.get('question', '').strip()
        explanation = row.get('explanation', '').strip()
        marks = int(row.get('marks', 1))
        
        if not question_content:
            raise ValueError("Question content is required")
        
        # Map CSV types to model types
        if question_type in ['MC', 'MCQ', 'MULTIPLE_CHOICE']:
            question_type = 'MCQ'
        elif question_type in ['TF', 'TRUE_FALSE', 'TRUEFALSE']:
            question_type = 'TF'
        elif question_type in ['SA', 'SHORT_ANSWER', 'SUBJECTIVE']:
            question_type = 'SUBJECTIVE'
        else:
            raise ValueError(f"Invalid question type: {question_type}")
        
        # Create question
        question = Question.objects.create(
            question_type=question_type,
            content=question_content,
            explanation=explanation,
            marks=marks,
            category=self.quiz.category,
            created_by=self.user
        )
        question.quiz.add(self.quiz)
        
        # Handle choices
        self._create_csv_choices(question, question_type, row)
        
        self.questions_created += 1
    
    def _create_csv_choices(self, question, question_type, row):
        """Create choices from CSV row data."""
        if question_type == 'MCQ':
            choices = []
            has_correct = False
            
            for i in range(1, 6):  # Support up to 5 choices
                choice_key = f'choice_{i}'
                correct_key = f'correct_{i}'
                
                choice_content = row.get(choice_key, '').strip()
                if choice_content:
                    is_correct = row.get(correct_key, '').lower() in ['true', '1', 'yes', 'y']
                    if is_correct:
                        has_correct = True
                    
                    choices.append({
                        'content': choice_content,
                        'is_correct': is_correct,
                        'order': i
                    })
            
            if not choices:
                raise ValueError("MCQ questions must have at least one choice")
            
            if not has_correct:
                raise ValueError("MCQ questions must have at least one correct answer")
            
            # Create choices
            for choice_data in choices:
                MCQChoice.objects.create(
                    question=question,
                    **choice_data
                )
        
        elif question_type == 'TF':
            correct_answer = row.get('correct_answer', 'true').lower() in ['true', '1', 'yes', 'y']
            
            MCQChoice.objects.create(
                question=question,
                content='True',
                is_correct=correct_answer,
                order=1
            )
            MCQChoice.objects.create(
                question=question,
                content='False',
                is_correct=not correct_answer,
                order=2
            )


def get_csv_template():
    """Generate CSV template for question import."""
    template_data = [
        {
            'type': 'MCQ',
            'question': 'What is the capital of France?',
            'explanation': 'Paris is the capital and largest city of France.',
            'marks': '1',
            'choice_1': 'Paris',
            'correct_1': 'true',
            'choice_2': 'London',
            'correct_2': 'false',
            'choice_3': 'Berlin',
            'correct_3': 'false',
            'choice_4': 'Madrid',
            'correct_4': 'false',
            'choice_5': '',
            'correct_5': ''
        },
        {
            'type': 'TF',
            'question': 'Python is a programming language.',
            'explanation': 'Python is indeed a high-level programming language.',
            'marks': '1',
            'correct_answer': 'true',
            'choice_1': '',
            'correct_1': '',
            'choice_2': '',
            'correct_2': '',
            'choice_3': '',
            'correct_3': '',
            'choice_4': '',
            'correct_4': '',
            'choice_5': '',
            'correct_5': ''
        },
        {
            'type': 'SA',
            'question': 'Explain the concept of object-oriented programming.',
            'explanation': 'Students should mention classes, objects, inheritance, encapsulation, etc.',
            'marks': '5',
            'choice_1': '',
            'correct_1': '',
            'choice_2': '',
            'correct_2': '',
            'choice_3': '',
            'correct_3': '',
            'choice_4': '',
            'correct_4': '',
            'choice_5': '',
            'correct_5': '',
            'correct_answer': ''
        }
    ]
    
    return template_data
