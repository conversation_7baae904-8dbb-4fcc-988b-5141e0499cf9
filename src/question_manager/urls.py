from django.urls import path
from . import views

app_name = 'question_manager'

urlpatterns = [
    # Question Management for Quizzes
    path('quiz/<int:quiz_id>/questions/', views.quiz_questions, name='quiz_questions'),
    path('quiz/<int:quiz_id>/questions/add/', views.add_question_modal, name='add_question_modal'),
    path('quiz/<int:quiz_id>/questions/bulk-add/', views.bulk_add_questions, name='bulk_add_questions'),
    
    # Question Banks
    path('banks/', views.QuestionBankListView.as_view(), name='question_bank_list'),
    path('banks/create/', views.QuestionBankCreateView.as_view(), name='question_bank_create'),
    path('banks/<int:pk>/', views.QuestionBankDetailView.as_view(), name='question_bank_detail'),
    path('banks/<int:pk>/edit/', views.QuestionBankUpdateView.as_view(), name='question_bank_edit'),
    
    # Question Templates
    path('templates/', views.QuestionTemplateListView.as_view(), name='question_template_list'),
    path('templates/create/', views.QuestionTemplateCreateView.as_view(), name='question_template_create'),
    
    # Utilities
    path('csv-template/', views.download_csv_template, name='csv_template'),
    path('import-history/', views.ImportHistoryView.as_view(), name='import_history'),
]
