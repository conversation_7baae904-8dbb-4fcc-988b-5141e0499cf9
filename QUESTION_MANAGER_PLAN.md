# Question Manager System - Complete Architecture Plan

## 🎯 **Comprehensive Question Management System**

Following Django best practices, I've created a dedicated `question_manager` app for handling all question-related functionality with proper separation of concerns.

## ✅ **What's Been Implemented**

### 🏗️ **Dedicated Question Manager App**
- **Separate App**: `question_manager` for clean separation of concerns
- **Models**: QuestionTemplate, BulkQuestionImport, QuestionBank
- **Views**: Complete CRUD operations for question management
- **Utils**: Text parsing and CSV parsing utilities
- **Admin**: Django admin integration for question management

### 📝 **Question Input Methods**

#### **1. Modal Form (Single Question)**
- **URL**: `/questions/quiz/{quiz_id}/questions/add/`
- **Features**: 
  - Interactive modal form
  - Support for MCQ, True/False, and Subjective questions
  - Real-time validation
  - HTMX integration for seamless UX

#### **2. Text Parsing (Bulk Import)**
- **Format Support**:
  ```
  MC:
  [Question text]
  Correct answer
  Incorrect answer
  Incorrect answer
  
  TF:
  [Question text]
  TRUE
  
  SA:
  [Question text]
  (Expected answer guidance)
  ```
- **Features**:
  - Automatic question type detection
  - First choice is correct for MCQ by default
  - True/False parsing with flexible input
  - Error handling and validation

#### **3. CSV Upload (Structured Import)**
- **CSV Template**: Downloadable template with examples
- **Columns**:
  - `type`: MCQ, TF, SA
  - `question`: Question content
  - `explanation`: Optional explanation
  - `marks`: Point value
  - `choice_1` to `choice_5`: Multiple choice options
  - `correct_1` to `correct_5`: Boolean flags for correct answers
  - `correct_answer`: For True/False questions
- **Features**:
  - Batch processing
  - Error logging
  - Import history tracking

### 🎲 **Question Types Supported**

#### **Multiple Choice (MCQ)**
- **Features**:
  - Up to 5 answer choices
  - Multiple correct answers supported
  - Randomized order in quiz display
  - Validation for at least one correct answer

#### **True/False (TF)**
- **Features**:
  - Automatic True/False choice generation
  - Flexible input parsing (TRUE, T, YES, Y, 1)
  - Simple binary choice display

#### **Subjective/Essay (SA)**
- **Features**:
  - Open-ended text responses
  - Manual grading support
  - Optional explanation/guidance

### 🏦 **Question Banks**
- **Organization**: Reusable question collections
- **Access Control**: Public/private with collaborator support
- **Categories**: Organized by quiz categories
- **Tagging**: Flexible organization system

### 📊 **Import Tracking**
- **Import History**: Track all bulk import operations
- **Error Logging**: Detailed error reporting
- **Status Tracking**: PENDING → PROCESSING → COMPLETED/FAILED
- **File Storage**: CSV files stored for reference

## 🔗 **Integration Points**

### **Quiz Admin Integration**
- **Quiz Detail**: New "Manage Questions" action card
- **Permission Checking**: Uses quiz permission system
- **Seamless Navigation**: Direct links from quiz management

### **URL Structure**
```
/questions/
├── quiz/{id}/questions/           # Question management for quiz
├── quiz/{id}/questions/add/       # Add single question modal
├── quiz/{id}/questions/bulk-add/  # Bulk import interface
├── banks/                         # Question bank management
├── templates/                     # Question templates
├── csv-template/                  # Download CSV template
└── import-history/                # View import history
```

### **Permission System**
- **Integration**: Uses existing quiz permission system
- **Validation**: Checks `can_manage_questions` permission
- **Access Control**: Only authorized users can manage questions

## 📁 **File Structure**

```
src/
├── question_manager/              # Dedicated question management app
│   ├── __init__.py
│   ├── apps.py
│   ├── models.py                  # QuestionTemplate, BulkQuestionImport, QuestionBank
│   ├── views.py                   # Question management views
│   ├── urls.py                    # Question management URLs
│   ├── utils.py                   # Text/CSV parsing utilities
│   ├── admin.py                   # Django admin integration
│   └── migrations/                # Database migrations
│
├── templates/question_manager/    # Question management templates
│   ├── quiz_questions.html        # Main question management interface
│   ├── add_question_modal.html    # Single question form
│   ├── bulk_add_questions.html    # Bulk import interface
│   ├── question_bank_list.html    # Question bank management
│   └── partials/                  # HTMX partial templates
│
└── quiz_app/
    └── models.py                  # Updated with TF question type
```

## 🎨 **User Experience Flow**

### **Question Management Workflow**
1. **Quiz Detail** → Click "Manage Questions"
2. **Question List** → View existing questions + Add new
3. **Add Questions** → Choose method:
   - **Single**: Modal form for one question
   - **Bulk Text**: Paste formatted text
   - **CSV Upload**: Upload structured file
4. **Review & Edit** → Manage created questions
5. **Question Banks** → Save to reusable collections

### **Text Parsing Example**
```
MC:
What is the capital of France?
Paris
London
Berlin
Madrid

TF:
Python is a programming language.
TRUE

SA:
Explain object-oriented programming concepts.
```

### **CSV Template Example**
| type | question | explanation | marks | choice_1 | correct_1 | choice_2 | correct_2 |
|------|----------|-------------|-------|----------|-----------|----------|-----------|
| MCQ  | Capital of France? | Paris is correct | 1 | Paris | true | London | false |
| TF   | Python is a language? | Basic fact | 1 | | | | |
| SA   | Explain OOP | Concepts needed | 5 | | | | |

## 🚀 **Next Steps to Complete**

### **Templates to Create**
1. **Main Interface**: `quiz_questions.html` - Question management dashboard
2. **Modal Form**: `add_question_modal.html` - Single question creation
3. **Bulk Import**: `bulk_add_questions.html` - Text/CSV import interface
4. **Question Banks**: Question bank management templates
5. **HTMX Partials**: Dynamic form components

### **Features to Implement**
1. **Question Editing**: Edit existing questions
2. **Question Deletion**: Remove questions with confirmation
3. **Question Reordering**: Drag-and-drop question ordering
4. **Question Preview**: Preview how questions appear in quiz
5. **Question Statistics**: Usage and performance tracking

### **Advanced Features**
1. **Question Templates**: Reusable question formats
2. **Question Pools**: Random selection from question banks
3. **Difficulty Levels**: Question difficulty classification
4. **Question Tagging**: Advanced organization system
5. **Collaborative Editing**: Multiple users editing questions

## 🔒 **Security & Validation**

### **Permission Checks**
- All question management requires `can_manage_questions` permission
- Question banks respect ownership and collaboration settings
- Import operations validate user permissions

### **Data Validation**
- **MCQ**: At least one correct answer required
- **Text Parsing**: Format validation and error handling
- **CSV Import**: Column validation and data type checking
- **File Upload**: File type and size validation

### **Error Handling**
- **Graceful Degradation**: Continue processing valid questions
- **Error Logging**: Detailed error messages and line numbers
- **User Feedback**: Clear success/error messages
- **Import Recovery**: Ability to retry failed imports

## 📊 **Benefits Achieved**

### **For Quiz Creators**
- **Multiple Input Methods**: Choose the most convenient method
- **Bulk Operations**: Efficiently create many questions
- **Reusable Content**: Question banks for repeated use
- **Error Prevention**: Validation and format checking

### **For Organizations**
- **Standardized Format**: Consistent question structure
- **Collaborative Creation**: Shared question banks
- **Quality Control**: Import validation and review process
- **Audit Trail**: Complete import history tracking

### **For Developers**
- **Clean Architecture**: Proper separation of concerns
- **Extensible Design**: Easy to add new question types
- **Maintainable Code**: Well-organized utilities and views
- **Django Best Practices**: Following framework conventions

## 🎉 **Summary**

The question management system provides:

✅ **Proper Separation**: Dedicated `question_manager` app
✅ **Multiple Input Methods**: Modal, text parsing, CSV upload
✅ **Question Type Support**: MCQ, True/False, Subjective
✅ **Randomized Responses**: MCQ choices shuffled in quiz
✅ **Bulk Operations**: Efficient question creation
✅ **Error Handling**: Robust validation and error reporting
✅ **Integration**: Seamless integration with existing quiz system

The foundation is complete and ready for template creation and feature implementation! 🚀
